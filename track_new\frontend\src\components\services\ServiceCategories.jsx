import React, { useState, useEffect } from 'react';
import { PlusIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';
import LoadingSpinner from '../common/LoadingSpinner';
import ErrorMessage from '../common/ErrorMessage';
import Modal from '../common/Modal';
import ConfirmDialog from '../common/ConfirmDialog';
import ServiceCategoryForm from './ServiceCategoryForm';
import CustomerCategoryForm from './CustomerCategoryForm';
import serviceCategoryService from '../../services/serviceCategoryService';
import customerCategoryService from '../../services/customerCategoryService';
import { showSuccess, showError } from '../common/NotificationSystem';

/**
 * Service Categories Management Component
 *
 * Exact replica of old project design with table-based layout
 */
const ServiceCategories = () => {
  // State management
  const [activeTab, setActiveTab] = useState('service'); // 'service' or 'customer'
  const [categories, setCategories] = useState([]);
  const [customerCategories, setCustomerCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [totalCount, setTotalCount] = useState(0);

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedCustomerCategory, setSelectedCustomerCategory] = useState(null);

  // Fetch service categories
  const fetchServiceCategories = async () => {
    try {
      setLoading(true);
      setError(null);

      const data = await serviceCategoryService.getServiceCategories({ limit: 1000 });

      if (data.success) {
        setCategories(data.data.categories || []);
        setTotalCount(data.data.pagination?.totalItems || data.data.categories?.length || 0);
      } else {
        throw new Error(data.message || 'Failed to fetch service categories');
      }
    } catch (err) {
      setError(err.message);
      console.error('Error fetching service categories:', err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch customer categories
  const fetchCustomerCategories = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await customerCategoryService.getCustomerCategories({ limit: 1000 });

      if (response.success) {
        setCustomerCategories(response.data.categories || []);
        setTotalCount(response.data.pagination?.totalItems || response.data.categories?.length || 0);
      } else {
        throw new Error(response.message || 'Failed to fetch customer categories');
      }
    } catch (err) {
      setError(err.message);
      console.error('Error fetching customer categories:', err);
    } finally {
      setLoading(false);
    }
  };

  // Effects
  useEffect(() => {
    if (activeTab === 'service') {
      fetchServiceCategories();
    } else if (activeTab === 'customer') {
      fetchCustomerCategories();
    }
  }, [activeTab]);

  // Handlers
  const handleTabChange = (tab) => {
    setActiveTab(tab);
    setError(null);
  };

  const handleCreate = () => {
    setSelectedCategory(null);
    setSelectedCustomerCategory(null);
    setShowCreateModal(true);
  };

  const handleEdit = (category) => {
    if (activeTab === 'service') {
      setSelectedCategory(category);
    } else {
      setSelectedCustomerCategory(category);
    }
    setShowEditModal(true);
  };

  const handleDelete = (category) => {
    if (activeTab === 'service') {
      setSelectedCategory(category);
    } else {
      setSelectedCustomerCategory(category);
    }
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    try {
      if (activeTab === 'service') {
        const data = await serviceCategoryService.deleteServiceCategory(selectedCategory.id);
        if (data.success) {
          await fetchServiceCategories();
          showSuccess('Service category deleted successfully');
        } else {
          throw new Error(data.message || 'Failed to delete service category');
        }
      } else {
        const data = await customerCategoryService.deleteCustomerCategory(selectedCustomerCategory.id);
        if (data.success) {
          await fetchCustomerCategories();
          showSuccess('Customer category deleted successfully');
        } else {
          throw new Error(data.message || 'Failed to delete customer category');
        }
      }

      setShowDeleteDialog(false);
      setSelectedCategory(null);
      setSelectedCustomerCategory(null);
    } catch (err) {
      setError(err.message);
      showError(err.message);
      console.error('Error deleting category:', err);
    }
  };

  const handleFormSuccess = async () => {
    if (activeTab === 'service') {
      await fetchServiceCategories();
    } else {
      await fetchCustomerCategories();
    }
    setShowCreateModal(false);
    setShowEditModal(false);
    setSelectedCategory(null);
    setSelectedCustomerCategory(null);
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Section - Matching Old Project Design */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Left side - Title and Count */}
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-900">Service Categories</h1>
              <span className="text-sm text-green-600 font-medium">
                Total {activeTab === 'service' ? 'Service' : 'Customer'} Categories: {totalCount}
              </span>
            </div>

            {/* Right side - Toggle buttons */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleTabChange('service')}
                className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeTab === 'service'
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Service
              </button>
              <button
                onClick={() => handleTabChange('customer')}
                className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeTab === 'customer'
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Customer
              </button>
            </div>
          </div>

          {/* New Category Button */}
          <div className="mt-4">
            <button
              onClick={handleCreate}
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              New {activeTab === 'service' ? 'Service' : 'Customer'} Category
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <ErrorMessage
          message={error}
          onDismiss={() => setError(null)}
        />
      )}

      {/* Table Section - Matching Old Project Design */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {activeTab === 'service' ? 'Service Category' : 'Customer Category'}
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {activeTab === 'service' ? 'Service Status' : 'Category Status'}
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {activeTab === 'service' ? 'Services Count' : 'Customers Count'}
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {activeTab === 'service' ? (
              categories.length > 0 ? (
                categories.map((category) => (
                  <tr key={category.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {category.service_category || category.serviceCategory}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        (category.service_status === 1 || category.serviceStatus === 1)
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {(category.service_status === 1 || category.serviceStatus === 1) ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {category.service_count || category.serviceCount || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <button
                        onClick={() => handleEdit(category)}
                        className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-xs font-medium text-gray-700 bg-white hover:bg-gray-50"
                      >
                        <PencilIcon className="h-3 w-3 mr-1" />
                        Edit Form
                      </button>
                      <button
                        onClick={() => handleDelete(category)}
                        className="inline-flex items-center px-3 py-1 border border-red-300 rounded-md text-xs font-medium text-red-700 bg-white hover:bg-red-50"
                      >
                        <TrashIcon className="h-3 w-3 mr-1" />
                        Delete
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="4" className="px-6 py-4 text-center text-sm text-gray-500">
                    No service categories found
                  </td>
                </tr>
              )
            ) : (
              customerCategories.length > 0 ? (
                customerCategories.map((category) => (
                  <tr key={category.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {category.category_name || category.categoryName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        category.is_active || category.isActive
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {category.is_active || category.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {category.customer_count || category.customerCount || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <button
                        onClick={() => handleEdit(category)}
                        className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-xs font-medium text-gray-700 bg-white hover:bg-gray-50"
                      >
                        <PencilIcon className="h-3 w-3 mr-1" />
                        Edit Form
                      </button>
                      <button
                        onClick={() => handleDelete(category)}
                        className="inline-flex items-center px-3 py-1 border border-red-300 rounded-md text-xs font-medium text-red-700 bg-white hover:bg-red-50"
                      >
                        <TrashIcon className="h-3 w-3 mr-1" />
                        Delete
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="4" className="px-6 py-4 text-center text-sm text-gray-500">
                    No customer categories found
                  </td>
                </tr>
              )
            )}
          </tbody>
        </table>
      </div>

      {/* Modals */}
      {/* Create/Edit Modal */}
      {(showCreateModal || showEditModal) && (
        <Modal
          isOpen={showCreateModal || showEditModal}
          onClose={() => {
            setShowCreateModal(false);
            setShowEditModal(false);
            setSelectedCategory(null);
            setSelectedCustomerCategory(null);
          }}
          title={`${showCreateModal ? 'Create' : 'Edit'} ${activeTab === 'service' ? 'Service' : 'Customer'} Category`}
        >
          {activeTab === 'service' ? (
            <ServiceCategoryForm
              category={selectedCategory}
              onSuccess={handleFormSuccess}
              onCancel={() => {
                setShowCreateModal(false);
                setShowEditModal(false);
                setSelectedCategory(null);
              }}
            />
          ) : (
            <CustomerCategoryForm
              customerCategory={selectedCustomerCategory}
              onSuccess={handleFormSuccess}
              onCancel={() => {
                setShowCreateModal(false);
                setShowEditModal(false);
                setSelectedCustomerCategory(null);
              }}
            />
          )}
        </Modal>
      )}

      {/* Delete Confirmation Dialog */}
      {showDeleteDialog && (
        <ConfirmDialog
          isOpen={showDeleteDialog}
          onClose={() => {
            setShowDeleteDialog(false);
            setSelectedCategory(null);
            setSelectedCustomerCategory(null);
          }}
          onConfirm={confirmDelete}
          title={`Delete ${activeTab === 'service' ? 'Service' : 'Customer'} Category`}
          message={`Are you sure you want to delete "${
            activeTab === 'service'
              ? (selectedCategory?.service_category || selectedCategory?.serviceCategory)
              : (selectedCustomerCategory?.category_name || selectedCustomerCategory?.categoryName)
          }"? This action cannot be undone.`}
          confirmText="Delete"
          cancelText="Cancel"
          type="danger"
        />
      )}
    </div>
  );
};
export default ServiceCategories;

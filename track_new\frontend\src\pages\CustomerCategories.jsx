import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Helmet } from 'react-helmet-async';
import { setPageTitle } from '../store/slices/uiSlice';
import CustomerCategoriesList from '../components/customerCategories/CustomerCategoriesList';
import CustomerCategoryForm from '../components/customerCategories/CustomerCategoryForm';
import CustomerCategoryView from '../components/customerCategories/CustomerCategoryView';
import CustomerCategoriesAnalytics from '../components/customerCategories/CustomerCategoriesAnalytics';
import LoadingSpinner from '../components/common/LoadingSpinner';
import ErrorMessage from '../components/common/ErrorMessage';

import {
  PlusIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  TagIcon,
  ChartBarIcon,
  CloudArrowUpIcon,
  CloudArrowDownIcon
} from '@heroicons/react/24/outline';

/**
 * Customer Categories Page Component
 *
 * Dedicated page for Customer Categories management with pixel-perfect TrackNew design
 * Features:
 * - Table and card view toggle
 * - Advanced search and filtering
 * - Category analytics
 * - Export/Import functionality
 * - Teal/orange color scheme with 30-40% height reduction
 * - Role-based access control
 */
const CustomerCategories = () => {
  const dispatch = useDispatch();

  // Local state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [categories, setCategories] = useState([]);
  const [totalCategories, setTotalCategories] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [viewMode, setViewMode] = useState('card'); // 'card' or 'table'
  const [activeTab, setActiveTab] = useState('categories'); // 'categories' or 'analytics'
  
  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all'); // 'all', 'active', 'inactive'
  const [sortBy, setSortBy] = useState('sortOrder');
  const [sortOrder, setSortOrder] = useState('asc');

  // Set page title
  useEffect(() => {
    dispatch(setPageTitle('Customer Categories'));
  }, [dispatch]);

  // Fetch categories function
  const fetchCategories = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        page: currentPage,
        limit: itemsPerPage,
        search: searchTerm,
        sortBy,
        sortOrder,
      };

      if (statusFilter !== 'all') {
        params.status = statusFilter === 'active' ? '1' : '0';
      }

      // Import service dynamically to avoid circular dependencies
      const { default: customerCategoryService } = await import('../services/customerCategoryService');
      const response = await customerCategoryService.getCustomerCategories(params);

      if (response.success) {
        setCategories(response.data.categories || []);
        setTotalCategories(response.data.pagination?.totalItems || 0);
      } else {
        throw new Error(response.message || 'Failed to fetch customer categories');
      }
    } catch (err) {
      setError(err.message);
      console.error('Error fetching customer categories:', err);
    } finally {
      setLoading(false);
    }
  }, [currentPage, itemsPerPage, searchTerm, statusFilter, sortBy, sortOrder]);

  // Initial load and refresh on dependencies
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  // Handlers
  const handleCreate = () => {
    setSelectedCategory(null);
    setShowCreateModal(true);
  };

  const handleEdit = (category) => {
    setSelectedCategory(category);
    setShowEditModal(true);
  };

  const handleView = (category) => {
    setSelectedCategory(category);
    setShowViewModal(true);
  };

  const handleDelete = async (category) => {
    if (window.confirm(`Are you sure you want to delete "${category.categoryName}"?`)) {
      try {
        const { default: customerCategoryService } = await import('../services/customerCategoryService');
        const response = await customerCategoryService.deleteCustomerCategory(category.id);
        
        if (response.success) {
          fetchCategories(); // Refresh list
        } else {
          throw new Error(response.message || 'Failed to delete category');
        }
      } catch (err) {
        setError(err.message);
      }
    }
  };

  const handleFormSuccess = () => {
    setShowCreateModal(false);
    setShowEditModal(false);
    setSelectedCategory(null);
    fetchCategories(); // Refresh list
  };

  const handleSearch = (term) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset to first page
  };

  const handleFilterChange = (filter) => {
    setStatusFilter(filter);
    setCurrentPage(1); // Reset to first page
  };

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
    setCurrentPage(1); // Reset to first page
  };

  const handleRefresh = () => {
    fetchCategories();
  };

  const handleExport = async () => {
    try {
      const { default: customerCategoryService } = await import('../services/customerCategoryService');
      await customerCategoryService.exportCustomerCategories({ format: 'xlsx' });
    } catch (err) {
      setError('Failed to export categories');
    }
  };

  // Calculate pagination
  const totalPages = Math.ceil(totalCategories / itemsPerPage);

  return (
    <>
      <Helmet>
        <title>Customer Categories - TrackNew</title>
        <meta name="description" content="Manage customer categories with discounts and special terms in TrackNew" />
      </Helmet>

      <div className="space-y-6">
        {/* Header Section */}
        <div className="bg-gradient-to-r from-teal-600 to-orange-500 rounded-lg shadow-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <TagIcon className="h-8 w-8" />
              <div>
                <h1 className="text-2xl font-bold">Customer Categories</h1>
                <p className="text-teal-100">Manage customer categories with discounts and special terms</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="text-right">
                <div className="text-2xl font-bold">{totalCategories}</div>
                <div className="text-sm text-teal-100">Total Categories</div>
              </div>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white rounded-lg shadow">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('categories')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'categories'
                    ? 'border-teal-500 text-teal-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <TagIcon className="h-5 w-5 inline mr-2" />
                Categories
              </button>
              <button
                onClick={() => setActiveTab('analytics')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'analytics'
                    ? 'border-teal-500 text-teal-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <ChartBarIcon className="h-5 w-5 inline mr-2" />
                Analytics
              </button>
            </nav>
          </div>
        </div>

        {/* Action Bar */}
        {activeTab === 'categories' && (
          <div className="bg-white rounded-lg shadow p-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleCreate}
                className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-teal-600 to-orange-500 text-white text-sm font-medium rounded-md hover:from-teal-700 hover:to-orange-600 transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                New Category
              </button>
              
              <button
                onClick={handleRefresh}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
              >
                <ArrowPathIcon className="h-4 w-4 mr-2" />
                Refresh
              </button>

              <button
                onClick={handleExport}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
              >
                <CloudArrowDownIcon className="h-4 w-4 mr-2" />
                Export
              </button>
            </div>

            <div className="flex items-center space-x-4">
              {/* View Mode Toggle */}
              <div className="flex rounded-md shadow-sm">
                <button
                  onClick={() => setViewMode('card')}
                  className={`px-3 py-2 text-sm font-medium rounded-l-md border ${
                    viewMode === 'card'
                      ? 'bg-teal-600 text-white border-teal-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  Cards
                </button>
                <button
                  onClick={() => setViewMode('table')}
                  className={`px-3 py-2 text-sm font-medium rounded-r-md border-t border-r border-b ${
                    viewMode === 'table'
                      ? 'bg-teal-600 text-white border-teal-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  Table
                </button>
              </div>
            </div>
          </div>
        </div>
        )}

        {/* Error Message */}
        {error && (
          <ErrorMessage
            message={error}
            onDismiss={() => setError(null)}
          />
        )}

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center py-8">
            <LoadingSpinner size="lg" />
          </div>
        )}

        {/* Categories List */}
        {!loading && activeTab === 'categories' && (
          <CustomerCategoriesList
            categories={categories}
            viewMode={viewMode}
            onEdit={handleEdit}
            onView={handleView}
            onDelete={handleDelete}
            searchTerm={searchTerm}
            onSearch={handleSearch}
            statusFilter={statusFilter}
            onFilterChange={handleFilterChange}
            sortBy={sortBy}
            sortOrder={sortOrder}
            onSort={handleSort}
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={totalCategories}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={setItemsPerPage}
          />
        )}

        {/* Analytics Dashboard */}
        {!loading && activeTab === 'analytics' && (
          <CustomerCategoriesAnalytics
            categories={categories}
            className="mt-6"
          />
        )}

        {/* Modals */}
        {showCreateModal && (
          <CustomerCategoryForm
            isOpen={showCreateModal}
            onClose={() => setShowCreateModal(false)}
            onSuccess={handleFormSuccess}
            title="Create Customer Category"
          />
        )}

        {showEditModal && selectedCategory && (
          <CustomerCategoryForm
            isOpen={showEditModal}
            onClose={() => setShowEditModal(false)}
            onSuccess={handleFormSuccess}
            category={selectedCategory}
            title="Edit Customer Category"
          />
        )}

        {showViewModal && selectedCategory && (
          <CustomerCategoryView
            isOpen={showViewModal}
            onClose={() => setShowViewModal(false)}
            category={selectedCategory}
            onEdit={() => {
              setShowViewModal(false);
              setShowEditModal(true);
            }}
          />
        )}
      </div>
    </>
  );
};

export default CustomerCategories;

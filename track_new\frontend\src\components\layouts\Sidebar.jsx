import React, { useState, useMemo } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { clsx } from 'clsx';
import { usePermissions } from '../../hooks/usePermissions';
import { getUserRole, getRoleDisplayName } from '../../utils/auth';
import {
  HomeIcon,
  UsersIcon,
  WrenchScrewdriverIcon,
  BuildingOfficeIcon,
  CurrencyDollarIcon,
  CubeIcon,
  UserGroupIcon,
  DocumentTextIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  GlobeAltIcon,
  ShoppingCartIcon,
  DevicePhoneMobileIcon,
  BanknotesIcon,
  UserIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  Bars3Icon,
  EllipsisHorizontalIcon,
} from '@heroicons/react/24/outline';
import { selectUser } from '../../store/slices/authSlice';

/**
 * Sidebar Component
 *
 * Navigation sidebar for the dashboard layout
 */

const Sidebar = ({
  isOpen,
  isCollapsed: externalIsCollapsed,
  onToggle,
  onCollapse,
  isMobile
}) => {
  const location = useLocation();
  const user = useSelector(selectUser);
  const { isSalesMan } = usePermissions();
  const userRole = getUserRole();

  // State for managing sidebar collapse (internal state)
  const [internalIsCollapsed, setInternalIsCollapsed] = useState(false);

  // Use external collapse state if provided, otherwise use internal
  const isCollapsed = externalIsCollapsed !== undefined ? externalIsCollapsed : internalIsCollapsed;

  // State for managing dropdown expansions
  const [expandedItems, setExpandedItems] = useState({
    services: false,
    sales: false,
    expenses: false,
    inventory: false,
    reports: false,
    employees: false,
    settings: false,
    website: false,
    ecommerce: false,
    leads: false,
  });

  // Toggle dropdown expansion
  const toggleExpanded = (itemKey) => {
    if (isCollapsed) return; // Don't expand when collapsed

    setExpandedItems(prev => ({
      ...prev,
      [itemKey]: !prev[itemKey]
    }));
  };

  // Toggle sidebar collapse - use external handler if provided
  const toggleSidebarCollapse = () => {
    if (onCollapse) {
      onCollapse();
    } else {
      setInternalIsCollapsed(prev => !prev);
    }
  };

  // Define all navigation items for sidebar based on old project structure
  // Reference: track_new_old\track-new-frontend\src\store\modules\sidebarandBottombarList.js
  const allNavigation = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: HomeIcon,
      allowedRoles: ['sub_admin', 'account_manager', 'service_manager', 'service_engineer', 'sales_man'],
      navigationKey: 'dashboard'
    },
    {
      name: 'Services',
      href: '/services',
      icon: WrenchScrewdriverIcon,
      allowedRoles: ['sub_admin', 'account_manager', 'service_manager', 'service_engineer'],
      navigationKey: 'services',
      hasDropdown: true,
      dropdownKey: 'services'
    },
    {
      name: 'Customers',
      href: '/customers',
      icon: UsersIcon,
      allowedRoles: ['sub_admin', 'account_manager', 'service_manager', 'service_engineer'],
      navigationKey: 'customers',
      hasDropdown: true,
      dropdownKey: 'customers'
    },
    {
      name: 'Leads',
      href: '/leads',
      icon: UserGroupIcon,
      allowedRoles: ['sub_admin', 'account_manager', 'service_manager', 'service_engineer'],
      navigationKey: 'leads',
      hasDropdown: true,
      dropdownKey: 'leads'
    },
    {
      name: 'Sales',
      href: '/sales',
      icon: CurrencyDollarIcon,
      allowedRoles: ['sub_admin', 'account_manager', 'service_manager', 'service_engineer', 'sales_man'],
      navigationKey: 'sales',
      hasDropdown: true,
      dropdownKey: 'sales'
    },
    {
      name: 'Expenses',
      href: '/expenses',
      icon: BanknotesIcon,
      allowedRoles: ['sub_admin', 'account_manager'],
      navigationKey: 'expenses',
      hasDropdown: true,
      dropdownKey: 'expenses'
    },
    {
      name: 'Inventory',
      href: '/inventory',
      icon: CubeIcon,
      allowedRoles: ['sub_admin', 'account_manager'],
      navigationKey: 'inventory',
      hasDropdown: true,
      dropdownKey: 'inventory'
    },
    {
      name: 'Reports',
      href: '/reports',
      icon: ChartBarIcon,
      allowedRoles: ['sub_admin', 'account_manager', 'service_manager', 'service_engineer'],
      navigationKey: 'reports',
      hasDropdown: true,
      dropdownKey: 'reports'
    },
    {
      name: 'Employees',
      href: '/employees',
      icon: UserIcon,
      allowedRoles: ['sub_admin'],
      navigationKey: 'users',
      hasDropdown: true,
      dropdownKey: 'employees'
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: Cog6ToothIcon,
      allowedRoles: [],
      navigationKey: 'settings',
      hasDropdown: true,
      dropdownKey: 'settings'
    },
    {
      name: 'Website',
      href: '/website',
      icon: GlobeAltIcon,
      allowedRoles: ['sub_admin', 'account_manager'],
      navigationKey: 'website',
      isNew: true,
      hasDropdown: false
    },
    {
      name: 'Ecommerce',
      href: '/ecommerce',
      icon: ShoppingCartIcon,
      allowedRoles: ['sub_admin', 'account_manager'],
      navigationKey: 'ecommerce',
      isNew: true,
      hasDropdown: false
    },
  ];

  // Filter navigation based on user role
  const navigation = useMemo(() => {
    // TEMPORARY: Show all navigation items to fix the UI issue
    // TODO: Re-enable role filtering after debugging role detection

    // For now, show all navigation items regardless of role
    return allNavigation;

    // COMMENTED OUT: Role filtering logic (to be re-enabled later)
    /*
    // Additional debugging for token
    const token = localStorage.getItem('tracknew_token');
    console.log('Raw token:', token);
    if (token) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        console.log('Token payload:', payload);
      } catch (error) {
        console.error('Error decoding token:', error);
      }
    }

    if (!userRole) {
      console.log('No user role found, showing all navigation items for debugging');
      return allNavigation; // Show all items when no role (for debugging)
    }

    // Map admin and super_admin roles to sub_admin for compatibility
    let effectiveRole = userRole;
    if (userRole === 'admin' || userRole === 'super_admin') {
      effectiveRole = 'sub_admin';
      console.log(`Mapped ${userRole} role to sub_admin`);
    }

    // Special case for sales_man - only show dashboard and sales
    if (effectiveRole === 'sales_man') {
      console.log('Sales man role detected, filtering navigation');
      return allNavigation.filter(item =>
        item.navigationKey === 'dashboard' || item.navigationKey === 'sales'
      );
    }

    // Filter based on allowed roles
    const filteredNavigation = allNavigation.filter(item => {
      // If no allowedRoles specified, show to everyone
      if (!item.allowedRoles || item.allowedRoles.length === 0) return true;

      // Check if user's effective role is in the allowed roles
      const hasAccess = item.allowedRoles.includes(effectiveRole);
      console.log(`Navigation item "${item.name}": ${hasAccess ? 'ALLOWED' : 'DENIED'} for role "${effectiveRole}"`);
      return hasAccess;
    });

    console.log('Filtered navigation items:', filteredNavigation.map(item => item.name));
    return filteredNavigation;
    */
  }, [userRole, user]);

  const sidebarClasses = clsx(
    'flex flex-col h-screen transition-all duration-300 ease-in-out',
    'bg-slate-800 shadow-xl transform overflow-hidden border-r border-slate-700',
    'custom-scrollbar-hidden',
    {
      'w-60': (!isCollapsed && !isMobile) || isMobile, // Match old project width or full width on mobile
      'w-20': isCollapsed && !isMobile,
      '-translate-x-full': !isOpen,
      'translate-x-0': isOpen,
      'fixed inset-y-0 left-0 z-50': isMobile,
      'lg:translate-x-0 lg:static lg:inset-0': !isMobile,
    }
  );

  return (
    <div className={sidebarClasses}>
      {/* Enhanced Header Section matching old project */}
      <div className="flex items-center justify-between h-16 px-4 bg-slate-900 border-b border-slate-700">
        {/* Company Logo and Info */}
        <div
          className="flex items-center cursor-pointer hover:bg-slate-700 p-2 rounded-lg transition-colors"
          onClick={() => {
            // Navigate to dashboard
            window.location.href = '/dashboard';
          }}
        >
          {!isCollapsed && (
            <div className="flex items-center space-x-3">
              {/* Company Logo */}
              <div className="flex-shrink-0">
                {user?.company?.logo ? (
                  <img
                    className="h-10 w-10 rounded-full ring-2 ring-gray-300 object-cover bg-white"
                    src={user.company.logo}
                    alt="Company Logo"
                  />
                ) : (
                  <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-teal-500 flex items-center justify-center ring-2 ring-gray-300">
                    <span className="text-white text-sm font-bold">
                      {user?.company?.name?.charAt(0)?.toUpperCase() || 'TN'}
                    </span>
                  </div>
                )}
              </div>
              {/* Company Info */}
              <div className="text-xs">
                <p className="text-white line-clamp-1 font-medium">
                  {user?.company?.name || 'TrackNew'}
                </p>
                <p className="text-slate-300 line-clamp-1 overflow-hidden text-ellipsis whitespace-nowrap">
                  {user?.email || 'Service Management'}
                </p>
              </div>
            </div>
          )}
          {isCollapsed && (
            <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-teal-500 flex items-center justify-center ring-2 ring-gray-300">
              <span className="text-white text-sm font-bold">
                {user?.company?.name?.charAt(0)?.toUpperCase() || 'TN'}
              </span>
            </div>
          )}
        </div>

        {/* Three-dot toggle button - only show on desktop */}
        {!isMobile && (
          <button
            onClick={toggleSidebarCollapse}
            className="text-slate-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-slate-700 group"
            title={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            <EllipsisHorizontalIcon className="h-5 w-5" />
          </button>
        )}
      </div>

      {/* Enhanced User Profile Section - matching old project design */}
      {!isCollapsed && user && (
        <div className="p-3 border-b border-slate-700">
          <div className="flex items-center bg-slate-700 rounded-xl p-3 hover:bg-slate-600 transition-colors cursor-pointer">
            <div className="flex-shrink-0 relative">
              {user?.avatar ? (
                <img
                  className="w-10 h-10 rounded-full ring-2 ring-slate-500 object-cover"
                  src={user.avatar}
                  alt={user.name || 'User'}
                />
              ) : (
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-teal-500 rounded-full flex items-center justify-center text-white text-sm font-medium shadow-lg ring-2 ring-slate-500">
                  {user.name?.charAt(0)?.toUpperCase() || user.email?.charAt(0)?.toUpperCase() || 'U'}
                </div>
              )}
              {/* Online status indicator */}
              <div className="absolute w-3 h-3 bg-green-400 rounded-full border-2 border-slate-700 -bottom-0.5 -right-0.5"></div>
            </div>
            <div className="ml-3 flex-1 min-w-0">
              <p className="text-sm font-semibold text-white truncate">
                {user.name || 'User'}
              </p>
              <p className="text-xs text-slate-300 truncate">
                {user.email || '<EMAIL>'}
              </p>
              {/* Role badge */}
              <div className="flex items-center mt-1">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-1"></div>
                <span className="text-xs text-green-400 font-medium">
                  {getRoleDisplayName(userRole) || 'Online'}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Collapsed User Profile */}
      {isCollapsed && user && (
        <div className="p-2 border-b border-slate-700">
          <div className="relative group">
            {user?.avatar ? (
              <img
                className="w-10 h-10 rounded-full ring-2 ring-slate-500 object-cover mx-auto cursor-pointer hover:ring-blue-400 transition-all"
                src={user.avatar}
                alt={user.name || 'User'}
                title={`${user.name || 'User'} - ${getRoleDisplayName(userRole) || 'User'}`}
              />
            ) : (
              <div
                className="w-10 h-10 bg-gradient-to-br from-blue-500 to-teal-500 rounded-full flex items-center justify-center text-white text-sm font-medium shadow-lg mx-auto ring-2 ring-slate-500 cursor-pointer hover:ring-blue-400 transition-all"
                title={`${user.name || 'User'} - ${getRoleDisplayName(userRole) || 'User'}`}
              >
                {user.name?.charAt(0)?.toUpperCase() || user.email?.charAt(0)?.toUpperCase() || 'U'}
              </div>
            )}
            <div className="absolute w-3 h-3 bg-green-400 rounded-full border-2 border-slate-800 -bottom-0.5 -right-0.5"></div>
          </div>
        </div>
      )}

      {/* Enhanced Navigation with Modern Styling */}
      <nav className="flex-1 px-3 py-4 space-y-1 overflow-y-auto custom-scrollbar">
        {navigation.map((item) => {
          const isActive = location.pathname.startsWith(item.href);
          const isExpanded = item.hasDropdown && expandedItems[item.dropdownKey];

          if (item.hasDropdown) {
            return (
              <div key={item.name}>
                {/* Dropdown Header */}
                <button
                  onClick={() => toggleExpanded(item.dropdownKey)}
                  className={clsx(
                    'group flex items-center w-full px-3 py-2.5 text-sm font-medium rounded-xl transition-all duration-200',
                    {
                      'bg-gradient-to-r from-blue-500 to-teal-500 text-white shadow-lg': isActive,
                      'text-slate-300 hover:bg-slate-700 hover:text-white': !isActive,
                      'justify-center': isCollapsed,
                    }
                  )}
                  title={isCollapsed ? item.name : undefined}
                >
                  <item.icon
                    className={clsx(
                      'h-5 w-5',
                      {
                        'text-white': isActive,
                        'text-slate-300 group-hover:text-white': !isActive,
                        'mr-3': !isCollapsed,
                      }
                    )}
                  />
                  {!isCollapsed && (
                    <>
                      <span className="flex-1 text-left font-medium">{item.name}</span>
                      {item.isNew && (
                        <span className="ml-2 px-2 py-0.5 text-xs font-bold bg-gradient-to-r from-green-400 to-emerald-500 text-white rounded-full shadow-sm">
                          New
                        </span>
                      )}
                      {isExpanded ? (
                        <ChevronDownIcon className="h-4 w-4 ml-2 transition-transform" />
                      ) : (
                        <ChevronRightIcon className="h-4 w-4 ml-2 transition-transform" />
                      )}
                    </>
                  )}
                </button>

                {/* Enhanced Dropdown Content */}
                {!isCollapsed && isExpanded && (
                  <div className="ml-8 mt-2 space-y-1 border-l-2 border-slate-600 pl-4">
                    {item.dropdownKey === 'services' && (
                      <>
                        <NavLink
                          to="/services"
                          className={({ isActive }) => clsx(
                            "block px-3 py-2 text-sm rounded-lg transition-all duration-200 font-medium",
                            isActive
                              ? "text-blue-400 bg-slate-700 border-l-2 border-blue-400"
                              : "text-slate-400 hover:text-white hover:bg-slate-700"
                          )}
                        >
                          • Services
                        </NavLink>
                        <NavLink
                          to="/services/categories"
                          className={({ isActive }) => clsx(
                            "block px-3 py-2 text-sm rounded-md transition-colors",
                            isActive
                              ? "text-white bg-gray-600"
                              : "text-gray-400 hover:text-white hover:bg-gray-700"
                          )}
                        >
                          - Categories
                        </NavLink>
                        <NavLink
                          to="/amcs"
                          className={({ isActive }) => clsx(
                            "block px-3 py-2 text-sm rounded-md transition-colors",
                            isActive
                              ? "text-white bg-gray-600"
                              : "text-gray-400 hover:text-white hover:bg-gray-700"
                          )}
                        >
                          - AMC
                        </NavLink>
                        <NavLink
                          to="/rma"
                          className={({ isActive }) => clsx(
                            "block px-3 py-2 text-sm rounded-md transition-colors",
                            isActive
                              ? "text-white bg-gray-600"
                              : "text-gray-400 hover:text-white hover:bg-gray-700"
                          )}
                        >
                          - RMA
                        </NavLink>
                      </>
                    )}
                    {item.dropdownKey === 'customers' && (
                      <>
                        <NavLink
                          to="/customers"
                          className={({ isActive }) => clsx(
                            "block px-3 py-2 text-sm rounded-lg transition-all duration-200 font-medium",
                            isActive
                              ? "text-blue-400 bg-slate-700 border-l-2 border-blue-400"
                              : "text-slate-400 hover:text-white hover:bg-slate-700"
                          )}
                        >
                          • Customers
                        </NavLink>
                        <NavLink
                          to="/customer-categories"
                          className={({ isActive }) => clsx(
                            "block px-3 py-2 text-sm rounded-md transition-colors",
                            isActive
                              ? "text-white bg-gray-600"
                              : "text-gray-400 hover:text-white hover:bg-gray-700"
                          )}
                        >
                          - Categories
                        </NavLink>
                      </>
                    )}
                    {item.dropdownKey === 'inventory' && (
                      <>
                        <NavLink
                          to="/inventory"
                          className={({ isActive }) => clsx(
                            "block px-3 py-2 text-sm rounded-md transition-colors",
                            isActive
                              ? "text-white bg-gray-600"
                              : "text-gray-400 hover:text-white hover:bg-gray-700"
                          )}
                        >
                          - Items
                        </NavLink>
                        <NavLink
                          to="/inventory/purchase"
                          className={({ isActive }) => clsx(
                            "block px-3 py-2 text-sm rounded-md transition-colors",
                            isActive
                              ? "text-white bg-gray-600"
                              : "text-gray-400 hover:text-white hover:bg-gray-700"
                          )}
                        >
                          - Purchase
                        </NavLink>
                        <NavLink
                          to="/inventory/supplier"
                          className={({ isActive }) => clsx(
                            "block px-3 py-2 text-sm rounded-md transition-colors",
                            isActive
                              ? "text-white bg-gray-600"
                              : "text-gray-400 hover:text-white hover:bg-gray-700"
                          )}
                        >
                          - Supplier
                        </NavLink>
                        <NavLink
                          to="/inventory/warehouse"
                          className={({ isActive }) => clsx(
                            "block px-3 py-2 text-sm rounded-md transition-colors",
                            isActive
                              ? "text-white bg-gray-600"
                              : "text-gray-400 hover:text-white hover:bg-gray-700"
                          )}
                        >
                          - Warehouse
                        </NavLink>
                        <NavLink
                          to="/inventory/payment-out"
                          className={({ isActive }) => clsx(
                            "block px-3 py-2 text-sm rounded-md transition-colors",
                            isActive
                              ? "text-white bg-gray-600"
                              : "text-gray-400 hover:text-white hover:bg-gray-700"
                          )}
                        >
                          - Payment Out
                        </NavLink>
                        <NavLink
                          to="/inventory/stock-adjust"
                          className={({ isActive }) => clsx(
                            "block px-3 py-2 text-sm rounded-md transition-colors",
                            isActive
                              ? "text-white bg-gray-600"
                              : "text-gray-400 hover:text-white hover:bg-gray-700"
                          )}
                        >
                          - Stock Adjust
                        </NavLink>
                      </>
                    )}
                    {item.dropdownKey === 'leads' && (
                      <>
                        <NavLink
                          to="/leads"
                          className={({ isActive }) => clsx(
                            "block px-3 py-2 text-sm rounded-lg transition-all duration-200 font-medium",
                            isActive
                              ? "text-blue-400 bg-slate-700 border-l-2 border-blue-400"
                              : "text-slate-400 hover:text-white hover:bg-slate-700"
                          )}
                        >
                          • Leads
                        </NavLink>
                        <NavLink
                          to="/website-enquiry"
                          className={({ isActive }) => clsx(
                            "block px-3 py-2 text-sm rounded-md transition-colors",
                            isActive
                              ? "text-white bg-gray-600"
                              : "text-gray-400 hover:text-white hover:bg-gray-700"
                          )}
                        >
                          - Website Enquiry
                        </NavLink>
                      </>
                    )}
                    {item.dropdownKey === 'sales' && (
                      <>
                        <NavLink
                          to="/sales/invoice"
                          className={({ isActive }) => clsx(
                            "block px-3 py-2 text-sm rounded-md transition-colors",
                            isActive
                              ? "text-white bg-gray-600"
                              : "text-gray-400 hover:text-white hover:bg-gray-700"
                          )}
                        >
                          - Sales / Invoice
                        </NavLink>
                        <NavLink
                          to="/sales/payment-in"
                          className={({ isActive }) => clsx(
                            "block px-3 py-2 text-sm rounded-md transition-colors",
                            isActive
                              ? "text-white bg-gray-600"
                              : "text-gray-400 hover:text-white hover:bg-gray-700"
                          )}
                        >
                          - Payment In
                        </NavLink>
                        <NavLink
                          to="/sales/proforma"
                          className={({ isActive }) => clsx(
                            "block px-3 py-2 text-sm rounded-md transition-colors",
                            isActive
                              ? "text-white bg-gray-600"
                              : "text-gray-400 hover:text-white hover:bg-gray-700"
                          )}
                        >
                          - Proforma
                        </NavLink>
                        <NavLink
                          to="/sales/quote-estimate"
                          className={({ isActive }) => clsx(
                            "block px-3 py-2 text-sm rounded-md transition-colors",
                            isActive
                              ? "text-white bg-gray-600"
                              : "text-gray-400 hover:text-white hover:bg-gray-700"
                          )}
                        >
                          - Quote / Estimate
                        </NavLink>
                      </>
                    )}
                    {item.dropdownKey !== 'services' && item.dropdownKey !== 'inventory' && item.dropdownKey !== 'leads' && item.dropdownKey !== 'sales' && (
                      <NavLink
                        to={item.href}
                        className="block px-3 py-2 text-sm text-gray-400 hover:text-white hover:bg-gray-700 rounded-md"
                      >
                        View All
                      </NavLink>
                    )}
                  </div>
                )}
              </div>
            );
          }

          // Enhanced Regular navigation item (no dropdown)
          return (
            <NavLink
              key={item.name}
              to={item.href}
              className={clsx(
                'group flex items-center px-3 py-2.5 text-sm font-medium rounded-xl transition-all duration-200',
                {
                  'bg-gradient-to-r from-blue-500 to-teal-500 text-white shadow-lg': isActive,
                  'text-slate-300 hover:bg-slate-700 hover:text-white': !isActive,
                  'justify-center': isCollapsed,
                }
              )}
              title={isCollapsed ? item.name : undefined}
            >
              <item.icon
                className={clsx(
                  'h-5 w-5',
                  {
                    'text-white': isActive,
                    'text-slate-300 group-hover:text-white': !isActive,
                    'mr-3': !isCollapsed,
                  }
                )}
              />
              {!isCollapsed && (
                <>
                  <span className="flex-1 font-medium">{item.name}</span>
                  {item.isNew && (
                    <span className="ml-2 px-2 py-0.5 text-xs font-bold bg-gradient-to-r from-green-400 to-emerald-500 text-white rounded-full shadow-sm">
                      New
                    </span>
                  )}
                </>
              )}
            </NavLink>
          );
        })}
      </nav>

      {/* Enhanced Download Android App Section */}
      {!isCollapsed && (
        <div className="p-4 border-t border-slate-700">
          <button className="w-full flex items-center justify-center px-4 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-sm font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl">
            <DevicePhoneMobileIcon className="h-4 w-4 mr-2" />
            Download Mobile App
          </button>
        </div>
      )}

      {/* Collapsed Download Button */}
      {isCollapsed && (
        <div className="p-2 border-t border-slate-700">
          <button
            className="w-full flex items-center justify-center p-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl transition-all duration-200 shadow-lg"
            title="Download Mobile App"
          >
            <DevicePhoneMobileIcon className="h-4 w-4" />
          </button>
        </div>
      )}
    </div>
  );
};

export default Sidebar;

import React, { useState, useEffect } from 'react';
import { UserGroupIcon, XMarkIcon } from '@heroicons/react/24/outline';
import customerCategoryService from '../../services/customerCategoryService';
import LoadingSpinner from '../common/LoadingSpinner';
import Modal from '../common/Modal';
import '../../styles/design-system.css';

/**
 * Customer Category Form Component
 *
 * Enhanced form for creating and editing customer categories with modal support
 * Features:
 * - Modal or inline display
 * - TrackNew teal/orange design consistency
 * - Comprehensive validation
 * - All customer category fields
 */
const CustomerCategoryForm = ({
  customerCategory,
  category, // Alternative prop name for compatibility
  onSuccess,
  onCancel,
  onClose,
  isOpen = false,
  title = 'Customer Category'
}) => {
  // Use either customerCategory or category prop for compatibility
  const categoryData = customerCategory || category;
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Form state
  const [formData, setFormData] = useState({
    categoryName: '',
    description: '',
    color: '#3B82F6',
    isActive: true,
    sortOrder: 0,
    discountPercentage: 0,
    specialTerms: ''
  });

  // Initialize form data
  useEffect(() => {
    if (categoryData) {
      setFormData({
        categoryName: categoryData.categoryName || categoryData.category_name || '',
        description: categoryData.description || '',
        color: categoryData.color || '#14B8A6', // Teal color
        isActive: categoryData.isActive !== undefined ? categoryData.isActive :
                 categoryData.is_active !== undefined ? categoryData.is_active : true,
        sortOrder: categoryData.sortOrder || categoryData.sort_order || 0,
        discountPercentage: categoryData.discountPercentage || categoryData.discount_percentage || 0,
        specialTerms: categoryData.specialTerms || categoryData.special_terms || ''
      });
    }
  }, [categoryData]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : 
               type === 'number' ? parseFloat(value) || 0 : value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.categoryName.trim()) {
      newErrors.categoryName = 'Category name is required';
    }

    if (formData.discountPercentage < 0 || formData.discountPercentage > 100) {
      newErrors.discountPercentage = 'Discount percentage must be between 0 and 100';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      let response;

      if (categoryData) {
        // Update existing category
        response = await customerCategoryService.updateCustomerCategory(categoryData.id, formData);
      } else {
        // Create new category
        response = await customerCategoryService.createCustomerCategory(formData);
      }

      if (response.success) {
        onSuccess();
      } else {
        throw new Error(response.message || 'Failed to save customer category');
      }
    } catch (error) {
      console.error('Error saving customer category:', error);
      setErrors({ submit: error.message });
    } finally {
      setLoading(false);
    }
  };

  // Handle close
  const handleClose = () => {
    if (onClose) onClose();
    if (onCancel) onCancel();
  };

  const formContent = (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Category Name */}
      <div>
        <label htmlFor="categoryName" className="block text-sm font-medium text-gray-700">
          Category Name *
        </label>
        <input
          type="text"
          id="categoryName"
          name="categoryName"
          value={formData.categoryName}
          onChange={handleInputChange}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-teal-500 focus:border-teal-500"
          placeholder="Enter category name"
        />
        {errors.categoryName && <p className="mt-1 text-sm text-red-600">{errors.categoryName}</p>}
      </div>

      {/* Description */}
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
          Description
        </label>
        <textarea
          id="description"
          name="description"
          rows={3}
          value={formData.description}
          onChange={handleInputChange}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          placeholder="Enter category description"
        />
      </div>

      {/* Color and Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="color" className="block text-sm font-medium text-gray-700">
            Color
          </label>
          <div className="mt-1 flex items-center space-x-3">
            <input
              type="color"
              id="color"
              name="color"
              value={formData.color}
              onChange={handleInputChange}
              className="h-10 w-20 border border-gray-300 rounded-md"
            />
            <input
              type="text"
              value={formData.color}
              onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
              className="flex-1 border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="#3B82F6"
            />
          </div>
        </div>

        <div>
          <label htmlFor="sortOrder" className="block text-sm font-medium text-gray-700">
            Sort Order
          </label>
          <input
            type="number"
            id="sortOrder"
            name="sortOrder"
            value={formData.sortOrder}
            onChange={handleInputChange}
            min="0"
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Discount and Terms */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="discountPercentage" className="block text-sm font-medium text-gray-700">
            Discount Percentage (%)
          </label>
          <input
            type="number"
            id="discountPercentage"
            name="discountPercentage"
            value={formData.discountPercentage}
            onChange={handleInputChange}
            min="0"
            max="100"
            step="0.01"
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
          {errors.discountPercentage && <p className="mt-1 text-sm text-red-600">{errors.discountPercentage}</p>}
        </div>

        <div className="flex items-center">
          <input
            id="isActive"
            name="isActive"
            type="checkbox"
            checked={formData.isActive}
            onChange={handleInputChange}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
            Active Category
          </label>
        </div>
      </div>

      {/* Special Terms */}
      <div>
        <label htmlFor="specialTerms" className="block text-sm font-medium text-gray-700">
          Special Terms & Conditions
        </label>
        <textarea
          id="specialTerms"
          name="specialTerms"
          rows={3}
          value={formData.specialTerms}
          onChange={handleInputChange}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          placeholder="Enter any special terms for this customer category"
        />
      </div>

      {/* Error Message */}
      {errors.submit && (
        <div className="text-red-600 text-sm">{errors.submit}</div>
      )}

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
        <button
          type="button"
          onClick={handleClose}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-teal-600 to-orange-500 hover:from-teal-700 hover:to-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
        >
          {loading ? (
            <div className="flex items-center">
              <LoadingSpinner size="sm" />
              <span className="ml-2">Saving...</span>
            </div>
          ) : (
            categoryData ? 'Update Category' : 'Create Category'
          )}
        </button>
      </div>
    </form>
  );

  // Return modal wrapper if isOpen is true, otherwise return form content directly
  if (isOpen) {
    return (
      <Modal
        isOpen={isOpen}
        onClose={handleClose}
        title={title}
        size="lg"
      >
        {formContent}
      </Modal>
    );
  }

  return formContent;
};

export default CustomerCategoryForm;

import React from 'react';
import {
  XMarkIcon,
  PencilIcon,
  TagIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  CalendarIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import Modal from '../common/Modal';

/**
 * Customer Category View Component
 * 
 * Modal component for viewing customer category details with TrackNew design
 * Features:
 * - Comprehensive category information display
 * - Teal/orange color scheme
 * - Quick edit access
 * - Professional layout with icons
 */
const CustomerCategoryView = ({
  isOpen,
  onClose,
  category,
  onEdit
}) => {
  if (!category) return null;

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount || 0);
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Customer Category Details"
      size="lg"
    >
      <div className="space-y-6">
        {/* Header Section */}
        <div className="bg-gradient-to-r from-teal-50 to-orange-50 rounded-lg p-6 border border-gray-200">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-4">
              <div
                className="w-12 h-12 rounded-full flex items-center justify-center"
                style={{ backgroundColor: category.color || '#14B8A6' }}
              >
                <TagIcon className="h-6 w-6 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900">
                  {category.categoryName || category.category_name}
                </h2>
                <p className="text-sm text-gray-600 mt-1">
                  {category.description || 'No description provided'}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                (category.isActive || category.is_active)
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}>
                {(category.isActive || category.is_active) ? (
                  <>
                    <CheckCircleIcon className="h-4 w-4 mr-1" />
                    Active
                  </>
                ) : (
                  <>
                    <XCircleIcon className="h-4 w-4 mr-1" />
                    Inactive
                  </>
                )}
              </span>
            </div>
          </div>
        </div>

        {/* Details Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
              Basic Information
            </h3>
            
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <TagIcon className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-500">Category Name</p>
                  <p className="text-sm text-gray-900">
                    {category.categoryName || category.category_name}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div
                  className="w-5 h-5 rounded-full border border-gray-300"
                  style={{ backgroundColor: category.color || '#14B8A6' }}
                />
                <div>
                  <p className="text-sm font-medium text-gray-500">Color</p>
                  <p className="text-sm text-gray-900">
                    {category.color || '#14B8A6'}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <UserGroupIcon className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-500">Customer Count</p>
                  <p className="text-sm text-gray-900">
                    {category.customerCount || category.customer_count || 0} customers
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div className="h-5 w-5 flex items-center justify-center text-gray-400 font-bold text-xs">
                  #
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Sort Order</p>
                  <p className="text-sm text-gray-900">
                    {category.sortOrder || category.sort_order || 0}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Financial Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
              Financial Details
            </h3>
            
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <CurrencyDollarIcon className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-500">Discount Percentage</p>
                  <p className="text-sm text-gray-900">
                    {category.discountPercentage || category.discount_percentage || 0}%
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <DocumentTextIcon className="h-5 w-5 text-gray-400 mt-0.5" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-500">Special Terms</p>
                  <p className="text-sm text-gray-900 whitespace-pre-wrap">
                    {category.specialTerms || category.special_terms || 'No special terms defined'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Timestamps */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-sm font-semibold text-gray-900 mb-3">Record Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <CalendarIcon className="h-4 w-4 text-gray-400" />
              <span className="text-gray-500">Created:</span>
              <span className="text-gray-900">
                {formatDate(category.createdAt || category.created_at)}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <CalendarIcon className="h-4 w-4 text-gray-400" />
              <span className="text-gray-500">Updated:</span>
              <span className="text-gray-900">
                {formatDate(category.updatedAt || category.updated_at)}
              </span>
            </div>
          </div>
        </div>

        {/* Description Section */}
        {(category.description && category.description.trim()) && (
          <div className="bg-blue-50 rounded-lg p-4">
            <h3 className="text-sm font-semibold text-gray-900 mb-2">Description</h3>
            <p className="text-sm text-gray-700 whitespace-pre-wrap">
              {category.description}
            </p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-colors"
          >
            Close
          </button>
          <button
            type="button"
            onClick={onEdit}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-teal-600 to-orange-500 hover:from-teal-700 hover:to-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-all duration-200"
          >
            <PencilIcon className="h-4 w-4 mr-2" />
            Edit Category
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default CustomerCategoryView;

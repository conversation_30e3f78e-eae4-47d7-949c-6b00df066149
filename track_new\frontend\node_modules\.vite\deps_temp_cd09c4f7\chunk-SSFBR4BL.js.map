{"version": 3, "sources": ["../../@babel/runtime/helpers/esm/objectSpread2.js", "../../redux/es/redux.js"], "sourcesContent": ["import defineProperty from \"./defineProperty.js\";\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nexport { _objectSpread2 as default };", "import _objectSpread from '@babel/runtime/helpers/esm/objectSpread2';\n\n/**\n * Adapted from React: https://github.com/facebook/react/blob/master/packages/shared/formatProdErrorMessage.js\n *\n * Do not require this module directly! Use normal throw error calls. These messages will be replaced with error codes\n * during build.\n * @param {number} code\n */\nfunction formatProdErrorMessage(code) {\n  return \"Minified Redux error #\" + code + \"; visit https://redux.js.org/Errors?code=\" + code + \" for the full message or \" + 'use the non-minified dev environment for full errors. ';\n}\n\n// Inlined version of the `symbol-observable` polyfill\nvar $$observable = (function () {\n  return typeof Symbol === 'function' && Symbol.observable || '@@observable';\n})();\n\n/**\n * These are private action types reserved by Redux.\n * For any unknown actions, you must return the current state.\n * If the current state is undefined, you must return the initial state.\n * Do not reference these action types directly in your code.\n */\nvar randomString = function randomString() {\n  return Math.random().toString(36).substring(7).split('').join('.');\n};\n\nvar ActionTypes = {\n  INIT: \"@@redux/INIT\" + randomString(),\n  REPLACE: \"@@redux/REPLACE\" + randomString(),\n  PROBE_UNKNOWN_ACTION: function PROBE_UNKNOWN_ACTION() {\n    return \"@@redux/PROBE_UNKNOWN_ACTION\" + randomString();\n  }\n};\n\n/**\n * @param {any} obj The object to inspect.\n * @returns {boolean} True if the argument appears to be a plain object.\n */\nfunction isPlainObject(obj) {\n  if (typeof obj !== 'object' || obj === null) return false;\n  var proto = obj;\n\n  while (Object.getPrototypeOf(proto) !== null) {\n    proto = Object.getPrototypeOf(proto);\n  }\n\n  return Object.getPrototypeOf(obj) === proto;\n}\n\n// Inlined / shortened version of `kindOf` from https://github.com/jonschlinkert/kind-of\nfunction miniKindOf(val) {\n  if (val === void 0) return 'undefined';\n  if (val === null) return 'null';\n  var type = typeof val;\n\n  switch (type) {\n    case 'boolean':\n    case 'string':\n    case 'number':\n    case 'symbol':\n    case 'function':\n      {\n        return type;\n      }\n  }\n\n  if (Array.isArray(val)) return 'array';\n  if (isDate(val)) return 'date';\n  if (isError(val)) return 'error';\n  var constructorName = ctorName(val);\n\n  switch (constructorName) {\n    case 'Symbol':\n    case 'Promise':\n    case 'WeakMap':\n    case 'WeakSet':\n    case 'Map':\n    case 'Set':\n      return constructorName;\n  } // other\n\n\n  return type.slice(8, -1).toLowerCase().replace(/\\s/g, '');\n}\n\nfunction ctorName(val) {\n  return typeof val.constructor === 'function' ? val.constructor.name : null;\n}\n\nfunction isError(val) {\n  return val instanceof Error || typeof val.message === 'string' && val.constructor && typeof val.constructor.stackTraceLimit === 'number';\n}\n\nfunction isDate(val) {\n  if (val instanceof Date) return true;\n  return typeof val.toDateString === 'function' && typeof val.getDate === 'function' && typeof val.setDate === 'function';\n}\n\nfunction kindOf(val) {\n  var typeOfVal = typeof val;\n\n  if (process.env.NODE_ENV !== 'production') {\n    typeOfVal = miniKindOf(val);\n  }\n\n  return typeOfVal;\n}\n\n/**\n * @deprecated\n *\n * **We recommend using the `configureStore` method\n * of the `@reduxjs/toolkit` package**, which replaces `createStore`.\n *\n * Redux Toolkit is our recommended approach for writing Redux logic today,\n * including store setup, reducers, data fetching, and more.\n *\n * **For more details, please read this Redux docs page:**\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * `configureStore` from Redux Toolkit is an improved version of `createStore` that\n * simplifies setup and helps avoid common bugs.\n *\n * You should not be using the `redux` core package by itself today, except for learning purposes.\n * The `createStore` method from the core `redux` package will not be removed, but we encourage\n * all users to migrate to using Redux Toolkit for all Redux code.\n *\n * If you want to use `createStore` without this visual deprecation warning, use\n * the `legacy_createStore` import instead:\n *\n * `import { legacy_createStore as createStore} from 'redux'`\n *\n */\n\nfunction createStore(reducer, preloadedState, enhancer) {\n  var _ref2;\n\n  if (typeof preloadedState === 'function' && typeof enhancer === 'function' || typeof enhancer === 'function' && typeof arguments[3] === 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(0) : 'It looks like you are passing several store enhancers to ' + 'createStore(). This is not supported. Instead, compose them ' + 'together to a single function. See https://redux.js.org/tutorials/fundamentals/part-4-store#creating-a-store-with-enhancers for an example.');\n  }\n\n  if (typeof preloadedState === 'function' && typeof enhancer === 'undefined') {\n    enhancer = preloadedState;\n    preloadedState = undefined;\n  }\n\n  if (typeof enhancer !== 'undefined') {\n    if (typeof enhancer !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(1) : \"Expected the enhancer to be a function. Instead, received: '\" + kindOf(enhancer) + \"'\");\n    }\n\n    return enhancer(createStore)(reducer, preloadedState);\n  }\n\n  if (typeof reducer !== 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(2) : \"Expected the root reducer to be a function. Instead, received: '\" + kindOf(reducer) + \"'\");\n  }\n\n  var currentReducer = reducer;\n  var currentState = preloadedState;\n  var currentListeners = [];\n  var nextListeners = currentListeners;\n  var isDispatching = false;\n  /**\n   * This makes a shallow copy of currentListeners so we can use\n   * nextListeners as a temporary list while dispatching.\n   *\n   * This prevents any bugs around consumers calling\n   * subscribe/unsubscribe in the middle of a dispatch.\n   */\n\n  function ensureCanMutateNextListeners() {\n    if (nextListeners === currentListeners) {\n      nextListeners = currentListeners.slice();\n    }\n  }\n  /**\n   * Reads the state tree managed by the store.\n   *\n   * @returns {any} The current state tree of your application.\n   */\n\n\n  function getState() {\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(3) : 'You may not call store.getState() while the reducer is executing. ' + 'The reducer has already received the state as an argument. ' + 'Pass it down from the top reducer instead of reading it from the store.');\n    }\n\n    return currentState;\n  }\n  /**\n   * Adds a change listener. It will be called any time an action is dispatched,\n   * and some part of the state tree may potentially have changed. You may then\n   * call `getState()` to read the current state tree inside the callback.\n   *\n   * You may call `dispatch()` from a change listener, with the following\n   * caveats:\n   *\n   * 1. The subscriptions are snapshotted just before every `dispatch()` call.\n   * If you subscribe or unsubscribe while the listeners are being invoked, this\n   * will not have any effect on the `dispatch()` that is currently in progress.\n   * However, the next `dispatch()` call, whether nested or not, will use a more\n   * recent snapshot of the subscription list.\n   *\n   * 2. The listener should not expect to see all state changes, as the state\n   * might have been updated multiple times during a nested `dispatch()` before\n   * the listener is called. It is, however, guaranteed that all subscribers\n   * registered before the `dispatch()` started will be called with the latest\n   * state by the time it exits.\n   *\n   * @param {Function} listener A callback to be invoked on every dispatch.\n   * @returns {Function} A function to remove this change listener.\n   */\n\n\n  function subscribe(listener) {\n    if (typeof listener !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(4) : \"Expected the listener to be a function. Instead, received: '\" + kindOf(listener) + \"'\");\n    }\n\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(5) : 'You may not call store.subscribe() while the reducer is executing. ' + 'If you would like to be notified after the store has been updated, subscribe from a ' + 'component and invoke store.getState() in the callback to access the latest state. ' + 'See https://redux.js.org/api/store#subscribelistener for more details.');\n    }\n\n    var isSubscribed = true;\n    ensureCanMutateNextListeners();\n    nextListeners.push(listener);\n    return function unsubscribe() {\n      if (!isSubscribed) {\n        return;\n      }\n\n      if (isDispatching) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(6) : 'You may not unsubscribe from a store listener while the reducer is executing. ' + 'See https://redux.js.org/api/store#subscribelistener for more details.');\n      }\n\n      isSubscribed = false;\n      ensureCanMutateNextListeners();\n      var index = nextListeners.indexOf(listener);\n      nextListeners.splice(index, 1);\n      currentListeners = null;\n    };\n  }\n  /**\n   * Dispatches an action. It is the only way to trigger a state change.\n   *\n   * The `reducer` function, used to create the store, will be called with the\n   * current state tree and the given `action`. Its return value will\n   * be considered the **next** state of the tree, and the change listeners\n   * will be notified.\n   *\n   * The base implementation only supports plain object actions. If you want to\n   * dispatch a Promise, an Observable, a thunk, or something else, you need to\n   * wrap your store creating function into the corresponding middleware. For\n   * example, see the documentation for the `redux-thunk` package. Even the\n   * middleware will eventually dispatch plain object actions using this method.\n   *\n   * @param {Object} action A plain object representing “what changed”. It is\n   * a good idea to keep actions serializable so you can record and replay user\n   * sessions, or use the time travelling `redux-devtools`. An action must have\n   * a `type` property which may not be `undefined`. It is a good idea to use\n   * string constants for action types.\n   *\n   * @returns {Object} For convenience, the same action object you dispatched.\n   *\n   * Note that, if you use a custom middleware, it may wrap `dispatch()` to\n   * return something else (for example, a Promise you can await).\n   */\n\n\n  function dispatch(action) {\n    if (!isPlainObject(action)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(7) : \"Actions must be plain objects. Instead, the actual type was: '\" + kindOf(action) + \"'. You may need to add middleware to your store setup to handle dispatching other values, such as 'redux-thunk' to handle dispatching functions. See https://redux.js.org/tutorials/fundamentals/part-4-store#middleware and https://redux.js.org/tutorials/fundamentals/part-6-async-logic#using-the-redux-thunk-middleware for examples.\");\n    }\n\n    if (typeof action.type === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(8) : 'Actions may not have an undefined \"type\" property. You may have misspelled an action type string constant.');\n    }\n\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(9) : 'Reducers may not dispatch actions.');\n    }\n\n    try {\n      isDispatching = true;\n      currentState = currentReducer(currentState, action);\n    } finally {\n      isDispatching = false;\n    }\n\n    var listeners = currentListeners = nextListeners;\n\n    for (var i = 0; i < listeners.length; i++) {\n      var listener = listeners[i];\n      listener();\n    }\n\n    return action;\n  }\n  /**\n   * Replaces the reducer currently used by the store to calculate the state.\n   *\n   * You might need this if your app implements code splitting and you want to\n   * load some of the reducers dynamically. You might also need this if you\n   * implement a hot reloading mechanism for Redux.\n   *\n   * @param {Function} nextReducer The reducer for the store to use instead.\n   * @returns {void}\n   */\n\n\n  function replaceReducer(nextReducer) {\n    if (typeof nextReducer !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(10) : \"Expected the nextReducer to be a function. Instead, received: '\" + kindOf(nextReducer));\n    }\n\n    currentReducer = nextReducer; // This action has a similiar effect to ActionTypes.INIT.\n    // Any reducers that existed in both the new and old rootReducer\n    // will receive the previous state. This effectively populates\n    // the new state tree with any relevant data from the old one.\n\n    dispatch({\n      type: ActionTypes.REPLACE\n    });\n  }\n  /**\n   * Interoperability point for observable/reactive libraries.\n   * @returns {observable} A minimal observable of state changes.\n   * For more information, see the observable proposal:\n   * https://github.com/tc39/proposal-observable\n   */\n\n\n  function observable() {\n    var _ref;\n\n    var outerSubscribe = subscribe;\n    return _ref = {\n      /**\n       * The minimal observable subscription method.\n       * @param {Object} observer Any object that can be used as an observer.\n       * The observer object should have a `next` method.\n       * @returns {subscription} An object with an `unsubscribe` method that can\n       * be used to unsubscribe the observable from the store, and prevent further\n       * emission of values from the observable.\n       */\n      subscribe: function subscribe(observer) {\n        if (typeof observer !== 'object' || observer === null) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(11) : \"Expected the observer to be an object. Instead, received: '\" + kindOf(observer) + \"'\");\n        }\n\n        function observeState() {\n          if (observer.next) {\n            observer.next(getState());\n          }\n        }\n\n        observeState();\n        var unsubscribe = outerSubscribe(observeState);\n        return {\n          unsubscribe: unsubscribe\n        };\n      }\n    }, _ref[$$observable] = function () {\n      return this;\n    }, _ref;\n  } // When a store is created, an \"INIT\" action is dispatched so that every\n  // reducer returns their initial state. This effectively populates\n  // the initial state tree.\n\n\n  dispatch({\n    type: ActionTypes.INIT\n  });\n  return _ref2 = {\n    dispatch: dispatch,\n    subscribe: subscribe,\n    getState: getState,\n    replaceReducer: replaceReducer\n  }, _ref2[$$observable] = observable, _ref2;\n}\n/**\n * Creates a Redux store that holds the state tree.\n *\n * **We recommend using `configureStore` from the\n * `@reduxjs/toolkit` package**, which replaces `createStore`:\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * The only way to change the data in the store is to call `dispatch()` on it.\n *\n * There should only be a single store in your app. To specify how different\n * parts of the state tree respond to actions, you may combine several reducers\n * into a single reducer function by using `combineReducers`.\n *\n * @param {Function} reducer A function that returns the next state tree, given\n * the current state tree and the action to handle.\n *\n * @param {any} [preloadedState] The initial state. You may optionally specify it\n * to hydrate the state from the server in universal apps, or to restore a\n * previously serialized user session.\n * If you use `combineReducers` to produce the root reducer function, this must be\n * an object with the same shape as `combineReducers` keys.\n *\n * @param {Function} [enhancer] The store enhancer. You may optionally specify it\n * to enhance the store with third-party capabilities such as middleware,\n * time travel, persistence, etc. The only store enhancer that ships with Redux\n * is `applyMiddleware()`.\n *\n * @returns {Store} A Redux store that lets you read the state, dispatch actions\n * and subscribe to changes.\n */\n\nvar legacy_createStore = createStore;\n\n/**\n * Prints a warning in the console if it exists.\n *\n * @param {String} message The warning message.\n * @returns {void}\n */\nfunction warning(message) {\n  /* eslint-disable no-console */\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\n    console.error(message);\n  }\n  /* eslint-enable no-console */\n\n\n  try {\n    // This error was thrown as a convenience so that if you enable\n    // \"break on all exceptions\" in your console,\n    // it would pause the execution at this line.\n    throw new Error(message);\n  } catch (e) {} // eslint-disable-line no-empty\n\n}\n\nfunction getUnexpectedStateShapeWarningMessage(inputState, reducers, action, unexpectedKeyCache) {\n  var reducerKeys = Object.keys(reducers);\n  var argumentName = action && action.type === ActionTypes.INIT ? 'preloadedState argument passed to createStore' : 'previous state received by the reducer';\n\n  if (reducerKeys.length === 0) {\n    return 'Store does not have a valid reducer. Make sure the argument passed ' + 'to combineReducers is an object whose values are reducers.';\n  }\n\n  if (!isPlainObject(inputState)) {\n    return \"The \" + argumentName + \" has unexpected type of \\\"\" + kindOf(inputState) + \"\\\". Expected argument to be an object with the following \" + (\"keys: \\\"\" + reducerKeys.join('\", \"') + \"\\\"\");\n  }\n\n  var unexpectedKeys = Object.keys(inputState).filter(function (key) {\n    return !reducers.hasOwnProperty(key) && !unexpectedKeyCache[key];\n  });\n  unexpectedKeys.forEach(function (key) {\n    unexpectedKeyCache[key] = true;\n  });\n  if (action && action.type === ActionTypes.REPLACE) return;\n\n  if (unexpectedKeys.length > 0) {\n    return \"Unexpected \" + (unexpectedKeys.length > 1 ? 'keys' : 'key') + \" \" + (\"\\\"\" + unexpectedKeys.join('\", \"') + \"\\\" found in \" + argumentName + \". \") + \"Expected to find one of the known reducer keys instead: \" + (\"\\\"\" + reducerKeys.join('\", \"') + \"\\\". Unexpected keys will be ignored.\");\n  }\n}\n\nfunction assertReducerShape(reducers) {\n  Object.keys(reducers).forEach(function (key) {\n    var reducer = reducers[key];\n    var initialState = reducer(undefined, {\n      type: ActionTypes.INIT\n    });\n\n    if (typeof initialState === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(12) : \"The slice reducer for key \\\"\" + key + \"\\\" returned undefined during initialization. \" + \"If the state passed to the reducer is undefined, you must \" + \"explicitly return the initial state. The initial state may \" + \"not be undefined. If you don't want to set a value for this reducer, \" + \"you can use null instead of undefined.\");\n    }\n\n    if (typeof reducer(undefined, {\n      type: ActionTypes.PROBE_UNKNOWN_ACTION()\n    }) === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(13) : \"The slice reducer for key \\\"\" + key + \"\\\" returned undefined when probed with a random type. \" + (\"Don't try to handle '\" + ActionTypes.INIT + \"' or other actions in \\\"redux/*\\\" \") + \"namespace. They are considered private. Instead, you must return the \" + \"current state for any unknown actions, unless it is undefined, \" + \"in which case you must return the initial state, regardless of the \" + \"action type. The initial state may not be undefined, but can be null.\");\n    }\n  });\n}\n/**\n * Turns an object whose values are different reducer functions, into a single\n * reducer function. It will call every child reducer, and gather their results\n * into a single state object, whose keys correspond to the keys of the passed\n * reducer functions.\n *\n * @param {Object} reducers An object whose values correspond to different\n * reducer functions that need to be combined into one. One handy way to obtain\n * it is to use ES6 `import * as reducers` syntax. The reducers may never return\n * undefined for any action. Instead, they should return their initial state\n * if the state passed to them was undefined, and the current state for any\n * unrecognized action.\n *\n * @returns {Function} A reducer function that invokes every reducer inside the\n * passed object, and builds a state object with the same shape.\n */\n\n\nfunction combineReducers(reducers) {\n  var reducerKeys = Object.keys(reducers);\n  var finalReducers = {};\n\n  for (var i = 0; i < reducerKeys.length; i++) {\n    var key = reducerKeys[i];\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof reducers[key] === 'undefined') {\n        warning(\"No reducer provided for key \\\"\" + key + \"\\\"\");\n      }\n    }\n\n    if (typeof reducers[key] === 'function') {\n      finalReducers[key] = reducers[key];\n    }\n  }\n\n  var finalReducerKeys = Object.keys(finalReducers); // This is used to make sure we don't warn about the same\n  // keys multiple times.\n\n  var unexpectedKeyCache;\n\n  if (process.env.NODE_ENV !== 'production') {\n    unexpectedKeyCache = {};\n  }\n\n  var shapeAssertionError;\n\n  try {\n    assertReducerShape(finalReducers);\n  } catch (e) {\n    shapeAssertionError = e;\n  }\n\n  return function combination(state, action) {\n    if (state === void 0) {\n      state = {};\n    }\n\n    if (shapeAssertionError) {\n      throw shapeAssertionError;\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      var warningMessage = getUnexpectedStateShapeWarningMessage(state, finalReducers, action, unexpectedKeyCache);\n\n      if (warningMessage) {\n        warning(warningMessage);\n      }\n    }\n\n    var hasChanged = false;\n    var nextState = {};\n\n    for (var _i = 0; _i < finalReducerKeys.length; _i++) {\n      var _key = finalReducerKeys[_i];\n      var reducer = finalReducers[_key];\n      var previousStateForKey = state[_key];\n      var nextStateForKey = reducer(previousStateForKey, action);\n\n      if (typeof nextStateForKey === 'undefined') {\n        var actionType = action && action.type;\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(14) : \"When called with an action of type \" + (actionType ? \"\\\"\" + String(actionType) + \"\\\"\" : '(unknown type)') + \", the slice reducer for key \\\"\" + _key + \"\\\" returned undefined. \" + \"To ignore an action, you must explicitly return the previous state. \" + \"If you want this reducer to hold no value, you can return null instead of undefined.\");\n      }\n\n      nextState[_key] = nextStateForKey;\n      hasChanged = hasChanged || nextStateForKey !== previousStateForKey;\n    }\n\n    hasChanged = hasChanged || finalReducerKeys.length !== Object.keys(state).length;\n    return hasChanged ? nextState : state;\n  };\n}\n\nfunction bindActionCreator(actionCreator, dispatch) {\n  return function () {\n    return dispatch(actionCreator.apply(this, arguments));\n  };\n}\n/**\n * Turns an object whose values are action creators, into an object with the\n * same keys, but with every function wrapped into a `dispatch` call so they\n * may be invoked directly. This is just a convenience method, as you can call\n * `store.dispatch(MyActionCreators.doSomething())` yourself just fine.\n *\n * For convenience, you can also pass an action creator as the first argument,\n * and get a dispatch wrapped function in return.\n *\n * @param {Function|Object} actionCreators An object whose values are action\n * creator functions. One handy way to obtain it is to use ES6 `import * as`\n * syntax. You may also pass a single function.\n *\n * @param {Function} dispatch The `dispatch` function available on your Redux\n * store.\n *\n * @returns {Function|Object} The object mimicking the original object, but with\n * every action creator wrapped into the `dispatch` call. If you passed a\n * function as `actionCreators`, the return value will also be a single\n * function.\n */\n\n\nfunction bindActionCreators(actionCreators, dispatch) {\n  if (typeof actionCreators === 'function') {\n    return bindActionCreator(actionCreators, dispatch);\n  }\n\n  if (typeof actionCreators !== 'object' || actionCreators === null) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(16) : \"bindActionCreators expected an object or a function, but instead received: '\" + kindOf(actionCreators) + \"'. \" + \"Did you write \\\"import ActionCreators from\\\" instead of \\\"import * as ActionCreators from\\\"?\");\n  }\n\n  var boundActionCreators = {};\n\n  for (var key in actionCreators) {\n    var actionCreator = actionCreators[key];\n\n    if (typeof actionCreator === 'function') {\n      boundActionCreators[key] = bindActionCreator(actionCreator, dispatch);\n    }\n  }\n\n  return boundActionCreators;\n}\n\n/**\n * Composes single-argument functions from right to left. The rightmost\n * function can take multiple arguments as it provides the signature for\n * the resulting composite function.\n *\n * @param {...Function} funcs The functions to compose.\n * @returns {Function} A function obtained by composing the argument functions\n * from right to left. For example, compose(f, g, h) is identical to doing\n * (...args) => f(g(h(...args))).\n */\nfunction compose() {\n  for (var _len = arguments.length, funcs = new Array(_len), _key = 0; _key < _len; _key++) {\n    funcs[_key] = arguments[_key];\n  }\n\n  if (funcs.length === 0) {\n    return function (arg) {\n      return arg;\n    };\n  }\n\n  if (funcs.length === 1) {\n    return funcs[0];\n  }\n\n  return funcs.reduce(function (a, b) {\n    return function () {\n      return a(b.apply(void 0, arguments));\n    };\n  });\n}\n\n/**\n * Creates a store enhancer that applies middleware to the dispatch method\n * of the Redux store. This is handy for a variety of tasks, such as expressing\n * asynchronous actions in a concise manner, or logging every action payload.\n *\n * See `redux-thunk` package as an example of the Redux middleware.\n *\n * Because middleware is potentially asynchronous, this should be the first\n * store enhancer in the composition chain.\n *\n * Note that each middleware will be given the `dispatch` and `getState` functions\n * as named arguments.\n *\n * @param {...Function} middlewares The middleware chain to be applied.\n * @returns {Function} A store enhancer applying the middleware.\n */\n\nfunction applyMiddleware() {\n  for (var _len = arguments.length, middlewares = new Array(_len), _key = 0; _key < _len; _key++) {\n    middlewares[_key] = arguments[_key];\n  }\n\n  return function (createStore) {\n    return function () {\n      var store = createStore.apply(void 0, arguments);\n\n      var _dispatch = function dispatch() {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(15) : 'Dispatching while constructing your middleware is not allowed. ' + 'Other middleware would not be applied to this dispatch.');\n      };\n\n      var middlewareAPI = {\n        getState: store.getState,\n        dispatch: function dispatch() {\n          return _dispatch.apply(void 0, arguments);\n        }\n      };\n      var chain = middlewares.map(function (middleware) {\n        return middleware(middlewareAPI);\n      });\n      _dispatch = compose.apply(void 0, chain)(store.dispatch);\n      return _objectSpread(_objectSpread({}, store), {}, {\n        dispatch: _dispatch\n      });\n    };\n  };\n}\n\nexport { ActionTypes as __DO_NOT_USE__ActionTypes, applyMiddleware, bindActionCreators, combineReducers, compose, createStore, legacy_createStore };\n"], "mappings": ";;;;;AACA,SAAS,QAAQ,GAAG,GAAG;AACrB,MAAI,IAAI,OAAO,KAAK,CAAC;AACrB,MAAI,OAAO,uBAAuB;AAChC,QAAI,IAAI,OAAO,sBAAsB,CAAC;AACtC,UAAM,IAAI,EAAE,OAAO,SAAUA,IAAG;AAC9B,aAAO,OAAO,yBAAyB,GAAGA,EAAC,EAAE;AAAA,IAC/C,CAAC,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,EACxB;AACA,SAAO;AACT;AACA,SAAS,eAAe,GAAG;AACzB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,IAAI,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAC/C,QAAI,IAAI,QAAQ,OAAO,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAUA,IAAG;AAClD,sBAAe,GAAGA,IAAG,EAAEA,EAAC,CAAC;AAAA,IAC3B,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,CAAC,CAAC,IAAI,QAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUA,IAAG;AAChJ,aAAO,eAAe,GAAGA,IAAG,OAAO,yBAAyB,GAAGA,EAAC,CAAC;AAAA,IACnE,CAAC;AAAA,EACH;AACA,SAAO;AACT;;;ACPA,IAAI,eAAgB,WAAY;AAC9B,SAAO,OAAO,WAAW,cAAc,OAAO,cAAc;AAC9D,EAAG;AAQH,IAAI,eAAe,SAASC,gBAAe;AACzC,SAAO,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,CAAC,EAAE,MAAM,EAAE,EAAE,KAAK,GAAG;AACnE;AAEA,IAAI,cAAc;AAAA,EAChB,MAAM,iBAAiB,aAAa;AAAA,EACpC,SAAS,oBAAoB,aAAa;AAAA,EAC1C,sBAAsB,SAAS,uBAAuB;AACpD,WAAO,iCAAiC,aAAa;AAAA,EACvD;AACF;AAMA,SAAS,cAAc,KAAK;AAC1B,MAAI,OAAO,QAAQ,YAAY,QAAQ,KAAM,QAAO;AACpD,MAAI,QAAQ;AAEZ,SAAO,OAAO,eAAe,KAAK,MAAM,MAAM;AAC5C,YAAQ,OAAO,eAAe,KAAK;AAAA,EACrC;AAEA,SAAO,OAAO,eAAe,GAAG,MAAM;AACxC;AAGA,SAAS,WAAW,KAAK;AACvB,MAAI,QAAQ,OAAQ,QAAO;AAC3B,MAAI,QAAQ,KAAM,QAAO;AACzB,MAAI,OAAO,OAAO;AAElB,UAAQ,MAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,YACH;AACE,aAAO;AAAA,IACT;AAAA,EACJ;AAEA,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO;AAC/B,MAAI,OAAO,GAAG,EAAG,QAAO;AACxB,MAAI,QAAQ,GAAG,EAAG,QAAO;AACzB,MAAI,kBAAkB,SAAS,GAAG;AAElC,UAAQ,iBAAiB;AAAA,IACvB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,EACX;AAGA,SAAO,KAAK,MAAM,GAAG,EAAE,EAAE,YAAY,EAAE,QAAQ,OAAO,EAAE;AAC1D;AAEA,SAAS,SAAS,KAAK;AACrB,SAAO,OAAO,IAAI,gBAAgB,aAAa,IAAI,YAAY,OAAO;AACxE;AAEA,SAAS,QAAQ,KAAK;AACpB,SAAO,eAAe,SAAS,OAAO,IAAI,YAAY,YAAY,IAAI,eAAe,OAAO,IAAI,YAAY,oBAAoB;AAClI;AAEA,SAAS,OAAO,KAAK;AACnB,MAAI,eAAe,KAAM,QAAO;AAChC,SAAO,OAAO,IAAI,iBAAiB,cAAc,OAAO,IAAI,YAAY,cAAc,OAAO,IAAI,YAAY;AAC/G;AAEA,SAAS,OAAO,KAAK;AACnB,MAAI,YAAY,OAAO;AAEvB,MAAI,MAAuC;AACzC,gBAAY,WAAW,GAAG;AAAA,EAC5B;AAEA,SAAO;AACT;AA4BA,SAAS,YAAY,SAAS,gBAAgB,UAAU;AACtD,MAAI;AAEJ,MAAI,OAAO,mBAAmB,cAAc,OAAO,aAAa,cAAc,OAAO,aAAa,cAAc,OAAO,UAAU,CAAC,MAAM,YAAY;AAClJ,UAAM,IAAI,MAAM,QAAwC,uBAAuB,CAAC,IAAI,kQAA4Q;AAAA,EAClW;AAEA,MAAI,OAAO,mBAAmB,cAAc,OAAO,aAAa,aAAa;AAC3E,eAAW;AACX,qBAAiB;AAAA,EACnB;AAEA,MAAI,OAAO,aAAa,aAAa;AACnC,QAAI,OAAO,aAAa,YAAY;AAClC,YAAM,IAAI,MAAM,QAAwC,uBAAuB,CAAC,IAAI,iEAAiE,OAAO,QAAQ,IAAI,GAAG;AAAA,IAC7K;AAEA,WAAO,SAAS,WAAW,EAAE,SAAS,cAAc;AAAA,EACtD;AAEA,MAAI,OAAO,YAAY,YAAY;AACjC,UAAM,IAAI,MAAM,QAAwC,uBAAuB,CAAC,IAAI,qEAAqE,OAAO,OAAO,IAAI,GAAG;AAAA,EAChL;AAEA,MAAI,iBAAiB;AACrB,MAAI,eAAe;AACnB,MAAI,mBAAmB,CAAC;AACxB,MAAI,gBAAgB;AACpB,MAAI,gBAAgB;AASpB,WAAS,+BAA+B;AACtC,QAAI,kBAAkB,kBAAkB;AACtC,sBAAgB,iBAAiB,MAAM;AAAA,IACzC;AAAA,EACF;AAQA,WAAS,WAAW;AAClB,QAAI,eAAe;AACjB,YAAM,IAAI,MAAM,QAAwC,uBAAuB,CAAC,IAAI,sMAAgN;AAAA,IACtS;AAEA,WAAO;AAAA,EACT;AA0BA,WAAS,UAAU,UAAU;AAC3B,QAAI,OAAO,aAAa,YAAY;AAClC,YAAM,IAAI,MAAM,QAAwC,uBAAuB,CAAC,IAAI,iEAAiE,OAAO,QAAQ,IAAI,GAAG;AAAA,IAC7K;AAEA,QAAI,eAAe;AACjB,YAAM,IAAI,MAAM,QAAwC,uBAAuB,CAAC,IAAI,iTAAgU;AAAA,IACtZ;AAEA,QAAI,eAAe;AACnB,iCAA6B;AAC7B,kBAAc,KAAK,QAAQ;AAC3B,WAAO,SAAS,cAAc;AAC5B,UAAI,CAAC,cAAc;AACjB;AAAA,MACF;AAEA,UAAI,eAAe;AACjB,cAAM,IAAI,MAAM,QAAwC,uBAAuB,CAAC,IAAI,sJAA2J;AAAA,MACjP;AAEA,qBAAe;AACf,mCAA6B;AAC7B,UAAI,QAAQ,cAAc,QAAQ,QAAQ;AAC1C,oBAAc,OAAO,OAAO,CAAC;AAC7B,yBAAmB;AAAA,IACrB;AAAA,EACF;AA4BA,WAAS,SAAS,QAAQ;AACxB,QAAI,CAAC,cAAc,MAAM,GAAG;AAC1B,YAAM,IAAI,MAAM,QAAwC,uBAAuB,CAAC,IAAI,mEAAmE,OAAO,MAAM,IAAI,4UAA4U;AAAA,IACtf;AAEA,QAAI,OAAO,OAAO,SAAS,aAAa;AACtC,YAAM,IAAI,MAAM,QAAwC,uBAAuB,CAAC,IAAI,4GAA4G;AAAA,IAClM;AAEA,QAAI,eAAe;AACjB,YAAM,IAAI,MAAM,QAAwC,uBAAuB,CAAC,IAAI,oCAAoC;AAAA,IAC1H;AAEA,QAAI;AACF,sBAAgB;AAChB,qBAAe,eAAe,cAAc,MAAM;AAAA,IACpD,UAAE;AACA,sBAAgB;AAAA,IAClB;AAEA,QAAI,YAAY,mBAAmB;AAEnC,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,WAAW,UAAU,CAAC;AAC1B,eAAS;AAAA,IACX;AAEA,WAAO;AAAA,EACT;AAaA,WAAS,eAAe,aAAa;AACnC,QAAI,OAAO,gBAAgB,YAAY;AACrC,YAAM,IAAI,MAAM,QAAwC,uBAAuB,EAAE,IAAI,oEAAoE,OAAO,WAAW,CAAC;AAAA,IAC9K;AAEA,qBAAiB;AAKjB,aAAS;AAAA,MACP,MAAM,YAAY;AAAA,IACpB,CAAC;AAAA,EACH;AASA,WAAS,aAAa;AACpB,QAAI;AAEJ,QAAI,iBAAiB;AACrB,WAAO,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASZ,WAAW,SAASC,WAAU,UAAU;AACtC,YAAI,OAAO,aAAa,YAAY,aAAa,MAAM;AACrD,gBAAM,IAAI,MAAM,QAAwC,uBAAuB,EAAE,IAAI,gEAAgE,OAAO,QAAQ,IAAI,GAAG;AAAA,QAC7K;AAEA,iBAAS,eAAe;AACtB,cAAI,SAAS,MAAM;AACjB,qBAAS,KAAK,SAAS,CAAC;AAAA,UAC1B;AAAA,QACF;AAEA,qBAAa;AACb,YAAI,cAAc,eAAe,YAAY;AAC7C,eAAO;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,IACF,GAAG,KAAK,YAAY,IAAI,WAAY;AAClC,aAAO;AAAA,IACT,GAAG;AAAA,EACL;AAKA,WAAS;AAAA,IACP,MAAM,YAAY;AAAA,EACpB,CAAC;AACD,SAAO,QAAQ;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,MAAM,YAAY,IAAI,YAAY;AACvC;AAgCA,IAAI,qBAAqB;AAQzB,SAAS,QAAQ,SAAS;AAExB,MAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,UAAU,YAAY;AACzE,YAAQ,MAAM,OAAO;AAAA,EACvB;AAIA,MAAI;AAIF,UAAM,IAAI,MAAM,OAAO;AAAA,EACzB,SAAS,GAAG;AAAA,EAAC;AAEf;AAEA,SAAS,sCAAsC,YAAY,UAAU,QAAQ,oBAAoB;AAC/F,MAAI,cAAc,OAAO,KAAK,QAAQ;AACtC,MAAI,eAAe,UAAU,OAAO,SAAS,YAAY,OAAO,kDAAkD;AAElH,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,cAAc,UAAU,GAAG;AAC9B,WAAO,SAAS,eAAe,8BAA+B,OAAO,UAAU,IAAI,8DAA+D,YAAa,YAAY,KAAK,MAAM,IAAI;AAAA,EAC5L;AAEA,MAAI,iBAAiB,OAAO,KAAK,UAAU,EAAE,OAAO,SAAU,KAAK;AACjE,WAAO,CAAC,SAAS,eAAe,GAAG,KAAK,CAAC,mBAAmB,GAAG;AAAA,EACjE,CAAC;AACD,iBAAe,QAAQ,SAAU,KAAK;AACpC,uBAAmB,GAAG,IAAI;AAAA,EAC5B,CAAC;AACD,MAAI,UAAU,OAAO,SAAS,YAAY,QAAS;AAEnD,MAAI,eAAe,SAAS,GAAG;AAC7B,WAAO,iBAAiB,eAAe,SAAS,IAAI,SAAS,SAAS,OAAO,MAAO,eAAe,KAAK,MAAM,IAAI,gBAAiB,eAAe,QAAQ,8DAA8D,MAAO,YAAY,KAAK,MAAM,IAAI;AAAA,EAC5P;AACF;AAEA,SAAS,mBAAmB,UAAU;AACpC,SAAO,KAAK,QAAQ,EAAE,QAAQ,SAAU,KAAK;AAC3C,QAAI,UAAU,SAAS,GAAG;AAC1B,QAAI,eAAe,QAAQ,QAAW;AAAA,MACpC,MAAM,YAAY;AAAA,IACpB,CAAC;AAED,QAAI,OAAO,iBAAiB,aAAa;AACvC,YAAM,IAAI,MAAM,QAAwC,uBAAuB,EAAE,IAAI,gCAAiC,MAAM,8QAAmS;AAAA,IACja;AAEA,QAAI,OAAO,QAAQ,QAAW;AAAA,MAC5B,MAAM,YAAY,qBAAqB;AAAA,IACzC,CAAC,MAAM,aAAa;AAClB,YAAM,IAAI,MAAM,QAAwC,uBAAuB,EAAE,IAAI,gCAAiC,MAAM,2DAA4D,0BAA0B,YAAY,OAAO,sCAAwC,8QAA6R;AAAA,IAC5iB;AAAA,EACF,CAAC;AACH;AAmBA,SAAS,gBAAgB,UAAU;AACjC,MAAI,cAAc,OAAO,KAAK,QAAQ;AACtC,MAAI,gBAAgB,CAAC;AAErB,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,QAAI,MAAM,YAAY,CAAC;AAEvB,QAAI,MAAuC;AACzC,UAAI,OAAO,SAAS,GAAG,MAAM,aAAa;AACxC,gBAAQ,kCAAmC,MAAM,GAAI;AAAA,MACvD;AAAA,IACF;AAEA,QAAI,OAAO,SAAS,GAAG,MAAM,YAAY;AACvC,oBAAc,GAAG,IAAI,SAAS,GAAG;AAAA,IACnC;AAAA,EACF;AAEA,MAAI,mBAAmB,OAAO,KAAK,aAAa;AAGhD,MAAI;AAEJ,MAAI,MAAuC;AACzC,yBAAqB,CAAC;AAAA,EACxB;AAEA,MAAI;AAEJ,MAAI;AACF,uBAAmB,aAAa;AAAA,EAClC,SAAS,GAAG;AACV,0BAAsB;AAAA,EACxB;AAEA,SAAO,SAAS,YAAY,OAAO,QAAQ;AACzC,QAAI,UAAU,QAAQ;AACpB,cAAQ,CAAC;AAAA,IACX;AAEA,QAAI,qBAAqB;AACvB,YAAM;AAAA,IACR;AAEA,QAAI,MAAuC;AACzC,UAAI,iBAAiB,sCAAsC,OAAO,eAAe,QAAQ,kBAAkB;AAE3G,UAAI,gBAAgB;AAClB,gBAAQ,cAAc;AAAA,MACxB;AAAA,IACF;AAEA,QAAI,aAAa;AACjB,QAAI,YAAY,CAAC;AAEjB,aAAS,KAAK,GAAG,KAAK,iBAAiB,QAAQ,MAAM;AACnD,UAAI,OAAO,iBAAiB,EAAE;AAC9B,UAAI,UAAU,cAAc,IAAI;AAChC,UAAI,sBAAsB,MAAM,IAAI;AACpC,UAAI,kBAAkB,QAAQ,qBAAqB,MAAM;AAEzD,UAAI,OAAO,oBAAoB,aAAa;AAC1C,YAAI,aAAa,UAAU,OAAO;AAClC,cAAM,IAAI,MAAM,QAAwC,uBAAuB,EAAE,IAAI,yCAAyC,aAAa,MAAO,OAAO,UAAU,IAAI,MAAO,oBAAoB,kCAAmC,OAAO,gLAA2L;AAAA,MACza;AAEA,gBAAU,IAAI,IAAI;AAClB,mBAAa,cAAc,oBAAoB;AAAA,IACjD;AAEA,iBAAa,cAAc,iBAAiB,WAAW,OAAO,KAAK,KAAK,EAAE;AAC1E,WAAO,aAAa,YAAY;AAAA,EAClC;AACF;AAEA,SAAS,kBAAkB,eAAe,UAAU;AAClD,SAAO,WAAY;AACjB,WAAO,SAAS,cAAc,MAAM,MAAM,SAAS,CAAC;AAAA,EACtD;AACF;AAwBA,SAAS,mBAAmB,gBAAgB,UAAU;AACpD,MAAI,OAAO,mBAAmB,YAAY;AACxC,WAAO,kBAAkB,gBAAgB,QAAQ;AAAA,EACnD;AAEA,MAAI,OAAO,mBAAmB,YAAY,mBAAmB,MAAM;AACjE,UAAM,IAAI,MAAM,QAAwC,uBAAuB,EAAE,IAAI,iFAAiF,OAAO,cAAc,IAAI,6FAAsG;AAAA,EACvS;AAEA,MAAI,sBAAsB,CAAC;AAE3B,WAAS,OAAO,gBAAgB;AAC9B,QAAI,gBAAgB,eAAe,GAAG;AAEtC,QAAI,OAAO,kBAAkB,YAAY;AACvC,0BAAoB,GAAG,IAAI,kBAAkB,eAAe,QAAQ;AAAA,IACtE;AAAA,EACF;AAEA,SAAO;AACT;AAYA,SAAS,UAAU;AACjB,WAAS,OAAO,UAAU,QAAQ,QAAQ,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACxF,UAAM,IAAI,IAAI,UAAU,IAAI;AAAA,EAC9B;AAEA,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO,SAAU,KAAK;AACpB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO,MAAM,CAAC;AAAA,EAChB;AAEA,SAAO,MAAM,OAAO,SAAU,GAAG,GAAG;AAClC,WAAO,WAAY;AACjB,aAAO,EAAE,EAAE,MAAM,QAAQ,SAAS,CAAC;AAAA,IACrC;AAAA,EACF,CAAC;AACH;AAmBA,SAAS,kBAAkB;AACzB,WAAS,OAAO,UAAU,QAAQ,cAAc,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC9F,gBAAY,IAAI,IAAI,UAAU,IAAI;AAAA,EACpC;AAEA,SAAO,SAAUC,cAAa;AAC5B,WAAO,WAAY;AACjB,UAAI,QAAQA,aAAY,MAAM,QAAQ,SAAS;AAE/C,UAAI,YAAY,SAAS,WAAW;AAClC,cAAM,IAAI,MAAM,QAAwC,uBAAuB,EAAE,IAAI,wHAA6H;AAAA,MACpN;AAEA,UAAI,gBAAgB;AAAA,QAClB,UAAU,MAAM;AAAA,QAChB,UAAU,SAAS,WAAW;AAC5B,iBAAO,UAAU,MAAM,QAAQ,SAAS;AAAA,QAC1C;AAAA,MACF;AACA,UAAI,QAAQ,YAAY,IAAI,SAAU,YAAY;AAChD,eAAO,WAAW,aAAa;AAAA,MACjC,CAAC;AACD,kBAAY,QAAQ,MAAM,QAAQ,KAAK,EAAE,MAAM,QAAQ;AACvD,aAAO,eAAc,eAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,QACjD,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF;AACF;", "names": ["r", "randomString", "subscribe", "createStore"]}
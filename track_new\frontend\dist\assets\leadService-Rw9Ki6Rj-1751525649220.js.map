{"version": 3, "file": "leadService-Rw9Ki6Rj-1751525649220.js", "sources": ["../../src/services/leadService.js"], "sourcesContent": ["import api from './api.js';\n\nconst leadService = {\n  // Lead CRUD operations\n  async getLeads(params = {}) {\n    const response = await api.get('/leads', { params });\n    return response.data;\n  },\n\n  async getLead(id) {\n    const response = await api.get(`/leads/${id}`);\n    return response.data;\n  },\n\n  async createLead(leadData) {\n    const response = await api.post('/leads', leadData);\n    return response.data;\n  },\n\n  async updateLead(id, leadData) {\n    const response = await api.put(`/leads/${id}`, leadData);\n    return response.data;\n  },\n\n  async deleteLead(id) {\n    const response = await api.delete(`/leads/${id}`);\n    return response.data;\n  },\n\n  async getLeadStats() {\n    const response = await api.get('/leads/stats');\n    return response.data;\n  },\n\n  // Lead Types\n  async getLeadTypes(params = {}) {\n    const response = await api.get('/lead-types', { params });\n    return response.data;\n  },\n\n  async createLeadType(typeData) {\n    const response = await api.post('/lead-types', typeData);\n    return response.data;\n  },\n\n  async updateLeadType(id, typeData) {\n    const response = await api.put(`/lead-types/${id}`, typeData);\n    return response.data;\n  },\n\n  async deleteLeadType(id) {\n    const response = await api.delete(`/lead-types/${id}`);\n    return response.data;\n  },\n\n  // Lead Statuses\n  async getLeadStatuses(params = {}) {\n    const response = await api.get('/lead-statuses', { params });\n    return response.data;\n  },\n\n  async createLeadStatus(statusData) {\n    const response = await api.post('/lead-statuses', statusData);\n    return response.data;\n  },\n\n  async updateLeadStatus(id, statusData) {\n    const response = await api.put(`/lead-statuses/${id}`, statusData);\n    return response.data;\n  },\n\n  async deleteLeadStatus(id) {\n    const response = await api.delete(`/lead-statuses/${id}`);\n    return response.data;\n  },\n\n  // Enquiries\n  async getEnquiries(params = {}) {\n    const response = await api.get('/enquiries', { params });\n    return response.data;\n  },\n\n  async getEnquiry(id) {\n    const response = await api.get(`/enquiries/${id}`);\n    return response.data;\n  },\n\n  async createEnquiry(enquiryData) {\n    const response = await api.post('/enquiries', enquiryData);\n    return response.data;\n  },\n\n  async updateEnquiry(id, enquiryData) {\n    const response = await api.put(`/enquiries/${id}`, enquiryData);\n    return response.data;\n  },\n\n  async deleteEnquiry(id) {\n    const response = await api.delete(`/enquiries/${id}`);\n    return response.data;\n  },\n\n  async convertEnquiryToLead(id, conversionData) {\n    const response = await api.post(`/enquiries/${id}/convert-to-lead`, conversionData);\n    return response.data;\n  },\n\n  async getEnquiryStats() {\n    const response = await api.get('/enquiries/stats');\n    return response.data;\n  },\n\n  // Utility methods\n  getStatusColor(status) {\n    const statusColors = {\n      open: 'bg-blue-100 text-blue-800',\n      in_progress: 'bg-yellow-100 text-yellow-800',\n      qualified: 'bg-purple-100 text-purple-800',\n      converted: 'bg-green-100 text-green-800',\n      closed: 'bg-gray-100 text-gray-800',\n      lost: 'bg-red-100 text-red-800'\n    };\n    return statusColors[status] || 'bg-gray-100 text-gray-800';\n  },\n\n  getPriorityColor(priority) {\n    const priorityColors = {\n      low: 'bg-green-100 text-green-800',\n      medium: 'bg-yellow-100 text-yellow-800',\n      high: 'bg-orange-100 text-orange-800',\n      urgent: 'bg-red-100 text-red-800'\n    };\n    return priorityColors[priority] || 'bg-gray-100 text-gray-800';\n  },\n\n  getSourceIcon(source) {\n    const sourceIcons = {\n      website: 'fa-solid fa-globe',\n      phone: 'fa-solid fa-phone',\n      email: 'fa-solid fa-envelope',\n      referral: 'fa-solid fa-user-friends',\n      social: 'fa-solid fa-share-alt',\n      direct: 'fa-solid fa-handshake'\n    };\n    return sourceIcons[source] || 'fa-solid fa-question';\n  },\n\n  formatLeadData(lead) {\n    return {\n      ...lead,\n      statusColor: this.getStatusColor(lead.status),\n      priorityColor: this.getPriorityColor(lead.priority),\n      sourceIcon: this.getSourceIcon(lead.source),\n      formattedDate: lead.leadDate ? new Date(lead.leadDate).toLocaleDateString() : '',\n      formattedFollowUp: lead.followUp ? new Date(lead.followUp).toLocaleDateString() : '',\n      customerName: lead.customer?.name || 'Unknown Customer',\n      assignedUserName: lead.assignedUser?.name || 'Unassigned'\n    };\n  },\n\n  // Filter and search helpers\n  buildFilterParams(filters) {\n    const params = {};\n    \n    if (filters.search) params.search = filters.search;\n    if (filters.status && filters.status !== 'all') params.status = filters.status;\n    if (filters.priority && filters.priority !== 'all') params.priority = filters.priority;\n    if (filters.assignTo) params.assignTo = filters.assignTo;\n    if (filters.customerId) params.customerId = filters.customerId;\n    if (filters.source && filters.source !== 'all') params.source = filters.source;\n    if (filters.sortBy) params.sortBy = filters.sortBy;\n    if (filters.sortOrder) params.sortOrder = filters.sortOrder;\n    if (filters.page) params.page = filters.page;\n    if (filters.limit) params.limit = filters.limit;\n\n    return params;\n  },\n\n  // Status mapping for old project compatibility\n  mapOldStatus(oldStatus) {\n    const statusMap = {\n      0: 'open',\n      1: 'in_progress',\n      2: 'qualified',\n      3: 'converted',\n      4: 'closed',\n      5: 'lost',\n      'pending': 'open',\n      'progress': 'in_progress',\n      'completed': 'qualified',\n      'cancelled': 'closed',\n      'hold': 'closed'\n    };\n    return statusMap[oldStatus] || 'open';\n  },\n\n  // Get status counts for display\n  getStatusCounts(statusCounts) {\n    return [\n      { type: 'pending', total: statusCounts.pending || statusCounts.open || 0, icon: 'fa-solid fa-hourglass-half' },\n      { type: 'open', total: statusCounts.open || 0, icon: 'fa-regular fa-folder-open' },\n      { type: 'progress', total: statusCounts.in_progress || 0, icon: 'fa-solid fa-bars-progress' },\n      { type: 'completed', total: statusCounts.qualified || 0, icon: 'fa-solid fa-flag-checkered' },\n      { type: 'cancelled', total: statusCounts.closed || 0, icon: 'fa-solid fa-ban' },\n      { type: 'hold', total: statusCounts.lost || 0, icon: 'fa-solid fa-hand' },\n      { type: 'all', total: statusCounts.all || 0, icon: 'fa-solid fa-circle-check' }\n    ];\n  }\n};\n\nexport default leadService;\n"], "names": ["leadService", "params", "api", "id", "leadData", "typeData", "statusData", "enquiryData", "conversionData", "status", "priority", "source", "lead", "_a", "_b", "filters", "oldStatus", "statusCounts"], "mappings": "wNAEK,MAACA,EAAc,CAElB,MAAM,SAASC,EAAS,GAAI,CAE1B,OADiB,MAAMC,EAAI,IAAI,SAAU,CAAE,OAAAD,EAAQ,GACnC,IACjB,EAED,MAAM,QAAQE,EAAI,CAEhB,OADiB,MAAMD,EAAI,IAAI,UAAUC,CAAE,EAAE,GAC7B,IACjB,EAED,MAAM,WAAWC,EAAU,CAEzB,OADiB,MAAMF,EAAI,KAAK,SAAUE,CAAQ,GAClC,IACjB,EAED,MAAM,WAAWD,EAAIC,EAAU,CAE7B,OADiB,MAAMF,EAAI,IAAI,UAAUC,CAAE,GAAIC,CAAQ,GACvC,IACjB,EAED,MAAM,WAAWD,EAAI,CAEnB,OADiB,MAAMD,EAAI,OAAO,UAAUC,CAAE,EAAE,GAChC,IACjB,EAED,MAAM,cAAe,CAEnB,OADiB,MAAMD,EAAI,IAAI,cAAc,GAC7B,IACjB,EAGD,MAAM,aAAaD,EAAS,GAAI,CAE9B,OADiB,MAAMC,EAAI,IAAI,cAAe,CAAE,OAAAD,EAAQ,GACxC,IACjB,EAED,MAAM,eAAeI,EAAU,CAE7B,OADiB,MAAMH,EAAI,KAAK,cAAeG,CAAQ,GACvC,IACjB,EAED,MAAM,eAAeF,EAAIE,EAAU,CAEjC,OADiB,MAAMH,EAAI,IAAI,eAAeC,CAAE,GAAIE,CAAQ,GAC5C,IACjB,EAED,MAAM,eAAeF,EAAI,CAEvB,OADiB,MAAMD,EAAI,OAAO,eAAeC,CAAE,EAAE,GACrC,IACjB,EAGD,MAAM,gBAAgBF,EAAS,GAAI,CAEjC,OADiB,MAAMC,EAAI,IAAI,iBAAkB,CAAE,OAAAD,EAAQ,GAC3C,IACjB,EAED,MAAM,iBAAiBK,EAAY,CAEjC,OADiB,MAAMJ,EAAI,KAAK,iBAAkBI,CAAU,GAC5C,IACjB,EAED,MAAM,iBAAiBH,EAAIG,EAAY,CAErC,OADiB,MAAMJ,EAAI,IAAI,kBAAkBC,CAAE,GAAIG,CAAU,GACjD,IACjB,EAED,MAAM,iBAAiBH,EAAI,CAEzB,OADiB,MAAMD,EAAI,OAAO,kBAAkBC,CAAE,EAAE,GACxC,IACjB,EAGD,MAAM,aAAaF,EAAS,GAAI,CAE9B,OADiB,MAAMC,EAAI,IAAI,aAAc,CAAE,OAAAD,EAAQ,GACvC,IACjB,EAED,MAAM,WAAWE,EAAI,CAEnB,OADiB,MAAMD,EAAI,IAAI,cAAcC,CAAE,EAAE,GACjC,IACjB,EAED,MAAM,cAAcI,EAAa,CAE/B,OADiB,MAAML,EAAI,KAAK,aAAcK,CAAW,GACzC,IACjB,EAED,MAAM,cAAcJ,EAAII,EAAa,CAEnC,OADiB,MAAML,EAAI,IAAI,cAAcC,CAAE,GAAII,CAAW,GAC9C,IACjB,EAED,MAAM,cAAcJ,EAAI,CAEtB,OADiB,MAAMD,EAAI,OAAO,cAAcC,CAAE,EAAE,GACpC,IACjB,EAED,MAAM,qBAAqBA,EAAIK,EAAgB,CAE7C,OADiB,MAAMN,EAAI,KAAK,cAAcC,CAAE,mBAAoBK,CAAc,GAClE,IACjB,EAED,MAAM,iBAAkB,CAEtB,OADiB,MAAMN,EAAI,IAAI,kBAAkB,GACjC,IACjB,EAGD,eAAeO,EAAQ,CASrB,MARqB,CACnB,KAAM,4BACN,YAAa,gCACb,UAAW,gCACX,UAAW,8BACX,OAAQ,4BACR,KAAM,yBACP,EACmBA,CAAM,GAAK,2BAChC,EAED,iBAAiBC,EAAU,CAOzB,MANuB,CACrB,IAAK,8BACL,OAAQ,gCACR,KAAM,gCACN,OAAQ,yBACT,EACqBA,CAAQ,GAAK,2BACpC,EAED,cAAcC,EAAQ,CASpB,MARoB,CAClB,QAAS,oBACT,MAAO,oBACP,MAAO,uBACP,SAAU,2BACV,OAAQ,wBACR,OAAQ,uBACT,EACkBA,CAAM,GAAK,sBAC/B,EAED,eAAeC,EAAM,SACnB,MAAO,CACL,GAAGA,EACH,YAAa,KAAK,eAAeA,EAAK,MAAM,EAC5C,cAAe,KAAK,iBAAiBA,EAAK,QAAQ,EAClD,WAAY,KAAK,cAAcA,EAAK,MAAM,EAC1C,cAAeA,EAAK,SAAW,IAAI,KAAKA,EAAK,QAAQ,EAAE,mBAAkB,EAAK,GAC9E,kBAAmBA,EAAK,SAAW,IAAI,KAAKA,EAAK,QAAQ,EAAE,mBAAkB,EAAK,GAClF,eAAcC,EAAAD,EAAK,WAAL,YAAAC,EAAe,OAAQ,mBACrC,mBAAkBC,EAAAF,EAAK,eAAL,YAAAE,EAAmB,OAAQ,YAC9C,CACF,EAGD,kBAAkBC,EAAS,CACzB,MAAMd,EAAS,CAAE,EAEjB,OAAIc,EAAQ,SAAQd,EAAO,OAASc,EAAQ,QACxCA,EAAQ,QAAUA,EAAQ,SAAW,QAAOd,EAAO,OAASc,EAAQ,QACpEA,EAAQ,UAAYA,EAAQ,WAAa,QAAOd,EAAO,SAAWc,EAAQ,UAC1EA,EAAQ,WAAUd,EAAO,SAAWc,EAAQ,UAC5CA,EAAQ,aAAYd,EAAO,WAAac,EAAQ,YAChDA,EAAQ,QAAUA,EAAQ,SAAW,QAAOd,EAAO,OAASc,EAAQ,QACpEA,EAAQ,SAAQd,EAAO,OAASc,EAAQ,QACxCA,EAAQ,YAAWd,EAAO,UAAYc,EAAQ,WAC9CA,EAAQ,OAAMd,EAAO,KAAOc,EAAQ,MACpCA,EAAQ,QAAOd,EAAO,MAAQc,EAAQ,OAEnCd,CACR,EAGD,aAAae,EAAW,CActB,MAbkB,CAChB,EAAG,OACH,EAAG,cACH,EAAG,YACH,EAAG,YACH,EAAG,SACH,EAAG,OACH,QAAW,OACX,SAAY,cACZ,UAAa,YACb,UAAa,SACb,KAAQ,QACT,EACgBA,CAAS,GAAK,MAChC,EAGD,gBAAgBC,EAAc,CAC5B,MAAO,CACL,CAAE,KAAM,UAAW,MAAOA,EAAa,SAAWA,EAAa,MAAQ,EAAG,KAAM,4BAA8B,EAC9G,CAAE,KAAM,OAAQ,MAAOA,EAAa,MAAQ,EAAG,KAAM,2BAA6B,EAClF,CAAE,KAAM,WAAY,MAAOA,EAAa,aAAe,EAAG,KAAM,2BAA6B,EAC7F,CAAE,KAAM,YAAa,MAAOA,EAAa,WAAa,EAAG,KAAM,4BAA8B,EAC7F,CAAE,KAAM,YAAa,MAAOA,EAAa,QAAU,EAAG,KAAM,iBAAmB,EAC/E,CAAE,KAAM,OAAQ,MAAOA,EAAa,MAAQ,EAAG,KAAM,kBAAoB,EACzE,CAAE,KAAM,MAAO,MAAOA,EAAa,KAAO,EAAG,KAAM,0BAA0B,CAC9E,CACL,CACA"}
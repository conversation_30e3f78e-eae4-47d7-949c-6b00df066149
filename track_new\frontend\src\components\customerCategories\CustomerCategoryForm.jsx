import React, { useState, useEffect } from 'react';
import { UserGroupIcon, XMarkIcon } from '@heroicons/react/24/outline';
import customerCategoryService from '../../services/customerCategoryService';
import LoadingSpinner from '../common/LoadingSpinner';
import Modal from '../common/Modal';
import '../../styles/design-system.css';

/**
 * Customer Category Form Component
 *
 * Enhanced form for creating and editing customer categories with modal support
 * Features:
 * - Modal or inline display
 * - TrackNew teal/orange design consistency
 * - Comprehensive validation
 * - All customer category fields
 */
const CustomerCategoryForm = ({
  customerCategory,
  category, // Alternative prop name for compatibility
  onSuccess,
  onCancel,
  onClose,
  isOpen = false,
  title = 'Customer Category'
}) => {
  // Use either customerCategory or category prop for compatibility
  const categoryData = customerCategory || category;
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Form state
  const [formData, setFormData] = useState({
    categoryName: '',
    description: '',
    color: '#14B8A6', // Teal color
    isActive: true,
    sortOrder: 0,
    discountPercentage: 0,
    specialTerms: ''
  });

  // Initialize form data
  useEffect(() => {
    if (categoryData) {
      setFormData({
        categoryName: categoryData.categoryName || categoryData.category_name || '',
        description: categoryData.description || '',
        color: categoryData.color || '#14B8A6', // Teal color
        isActive: categoryData.isActive !== undefined ? categoryData.isActive :
                 categoryData.is_active !== undefined ? categoryData.is_active : true,
        sortOrder: categoryData.sortOrder || categoryData.sort_order || 0,
        discountPercentage: categoryData.discountPercentage || categoryData.discount_percentage || 0,
        specialTerms: categoryData.specialTerms || categoryData.special_terms || ''
      });
    }
  }, [categoryData]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : 
               type === 'number' ? parseFloat(value) || 0 : value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.categoryName.trim()) {
      newErrors.categoryName = 'Category name is required';
    }

    if (formData.discountPercentage < 0 || formData.discountPercentage > 100) {
      newErrors.discountPercentage = 'Discount percentage must be between 0 and 100';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      let response;

      if (categoryData) {
        // Update existing category
        response = await customerCategoryService.updateCustomerCategory(categoryData.id, formData);
      } else {
        // Create new category
        response = await customerCategoryService.createCustomerCategory(formData);
      }

      if (response.success) {
        onSuccess();
      } else {
        throw new Error(response.message || 'Failed to save customer category');
      }
    } catch (error) {
      console.error('Error saving customer category:', error);
      setErrors({ submit: error.message });
    } finally {
      setLoading(false);
    }
  };

  // Handle close
  const handleClose = () => {
    if (onClose) onClose();
    if (onCancel) onCancel();
  };

  const formContent = (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Category Name */}
      <div>
        <label htmlFor="categoryName" className="block text-sm font-medium text-gray-700 mb-1">
          Category Name *
        </label>
        <input
          type="text"
          id="categoryName"
          name="categoryName"
          value={formData.categoryName}
          onChange={handleInputChange}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent ${
            errors.categoryName ? 'border-red-300' : 'border-gray-300'
          }`}
          placeholder="Enter category name"
        />
        {errors.categoryName && (
          <p className="mt-1 text-sm text-red-600">{errors.categoryName}</p>
        )}
      </div>

      {/* Description */}
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
          Description
        </label>
        <textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleInputChange}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
          placeholder="Enter category description"
        />
      </div>

      {/* Color and Status Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="color" className="block text-sm font-medium text-gray-700 mb-1">
            Color
          </label>
          <div className="flex items-center space-x-2">
            <input
              type="color"
              id="color"
              name="color"
              value={formData.color}
              onChange={handleInputChange}
              className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
            />
            <input
              type="text"
              value={formData.color}
              onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
              placeholder="#14B8A6"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Status
          </label>
          <div className="flex items-center mt-2">
            <input
              type="checkbox"
              id="isActive"
              name="isActive"
              checked={formData.isActive}
              onChange={handleInputChange}
              className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
            />
            <label htmlFor="isActive" className="ml-2 text-sm text-gray-700">
              Active
            </label>
          </div>
        </div>
      </div>

      {/* Discount and Sort Order Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="discountPercentage" className="block text-sm font-medium text-gray-700 mb-1">
            Discount Percentage (%)
          </label>
          <input
            type="number"
            id="discountPercentage"
            name="discountPercentage"
            value={formData.discountPercentage}
            onChange={handleInputChange}
            min="0"
            max="100"
            step="0.01"
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent ${
              errors.discountPercentage ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="0.00"
          />
          {errors.discountPercentage && (
            <p className="mt-1 text-sm text-red-600">{errors.discountPercentage}</p>
          )}
        </div>

        <div>
          <label htmlFor="sortOrder" className="block text-sm font-medium text-gray-700 mb-1">
            Sort Order
          </label>
          <input
            type="number"
            id="sortOrder"
            name="sortOrder"
            value={formData.sortOrder}
            onChange={handleInputChange}
            min="0"
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            placeholder="0"
          />
        </div>
      </div>

      {/* Special Terms */}
      <div>
        <label htmlFor="specialTerms" className="block text-sm font-medium text-gray-700 mb-1">
          Special Terms
        </label>
        <textarea
          id="specialTerms"
          name="specialTerms"
          value={formData.specialTerms}
          onChange={handleInputChange}
          rows={2}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
          placeholder="Enter any special terms or conditions"
        />
      </div>

      {/* Submit Error */}
      {errors.submit && (
        <div className="text-red-600 text-sm">{errors.submit}</div>
      )}

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button
          type="button"
          onClick={handleClose}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-teal-600 to-orange-500 rounded-md hover:from-teal-700 hover:to-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <div className="flex items-center">
              <LoadingSpinner size="sm" className="mr-2" />
              {categoryData ? 'Updating...' : 'Creating...'}
            </div>
          ) : (
            categoryData ? 'Update Category' : 'Create Category'
          )}
        </button>
      </div>
    </form>
  );

  // If modal mode, wrap in Modal component
  if (isOpen) {
    return (
      <Modal
        isOpen={isOpen}
        onClose={handleClose}
        title={categoryData ? `Edit ${title}` : `Create ${title}`}
        size="lg"
      >
        {formContent}
      </Modal>
    );
  }

  // Otherwise return form directly
  return formContent;
};

export default CustomerCategoryForm;

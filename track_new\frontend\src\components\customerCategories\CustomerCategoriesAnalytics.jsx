import React, { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  TagIcon,
  UsersIcon,
  CurrencyDollarIcon,
  TrendingUpIcon,
  TrendingDownIcon
} from '@heroicons/react/24/outline';

/**
 * Customer Categories Analytics Component
 * 
 * Analytics dashboard for Customer Categories with TrackNew design consistency
 * Features:
 * - Category distribution charts
 * - Customer count statistics
 * - Discount analysis
 * - Status breakdown
 * - Teal/orange color scheme
 */
const CustomerCategoriesAnalytics = ({
  categories = [],
  className = ''
}) => {
  const [analytics, setAnalytics] = useState({
    totalCategories: 0,
    activeCategories: 0,
    inactiveCategories: 0,
    totalCustomers: 0,
    averageDiscount: 0,
    topCategories: [],
    discountDistribution: []
  });

  useEffect(() => {
    calculateAnalytics();
  }, [categories]);

  const calculateAnalytics = () => {
    if (!categories || categories.length === 0) {
      setAnalytics({
        totalCategories: 0,
        activeCategories: 0,
        inactiveCategories: 0,
        totalCustomers: 0,
        averageDiscount: 0,
        topCategories: [],
        discountDistribution: []
      });
      return;
    }

    const totalCategories = categories.length;
    const activeCategories = categories.filter(cat => cat.isActive || cat.is_active).length;
    const inactiveCategories = totalCategories - activeCategories;
    
    const totalCustomers = categories.reduce((sum, cat) => 
      sum + (cat.customerCount || cat.customer_count || 0), 0);
    
    const totalDiscount = categories.reduce((sum, cat) => 
      sum + parseFloat(cat.discountPercentage || cat.discount_percentage || 0), 0);
    const averageDiscount = totalCategories > 0 ? (totalDiscount / totalCategories).toFixed(2) : 0;

    // Top categories by customer count
    const topCategories = [...categories]
      .sort((a, b) => (b.customerCount || b.customer_count || 0) - (a.customerCount || a.customer_count || 0))
      .slice(0, 5)
      .map(cat => ({
        name: cat.categoryName || cat.category_name,
        customers: cat.customerCount || cat.customer_count || 0,
        color: cat.color || '#14B8A6'
      }));

    // Discount distribution
    const discountRanges = [
      { range: '0%', min: 0, max: 0, count: 0 },
      { range: '1-5%', min: 0.01, max: 5, count: 0 },
      { range: '6-10%', min: 6, max: 10, count: 0 },
      { range: '11-20%', min: 11, max: 20, count: 0 },
      { range: '20%+', min: 20, max: 100, count: 0 }
    ];

    categories.forEach(cat => {
      const discount = parseFloat(cat.discountPercentage || cat.discount_percentage || 0);
      discountRanges.forEach(range => {
        if (discount >= range.min && discount <= range.max) {
          range.count++;
        }
      });
    });

    setAnalytics({
      totalCategories,
      activeCategories,
      inactiveCategories,
      totalCustomers,
      averageDiscount,
      topCategories,
      discountDistribution: discountRanges.filter(range => range.count > 0)
    });
  };

  const StatCard = ({ title, value, icon: Icon, color = 'teal', trend = null }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          {trend && (
            <div className={`flex items-center mt-2 text-sm ${
              trend.direction === 'up' ? 'text-green-600' : 'text-red-600'
            }`}>
              {trend.direction === 'up' ? (
                <TrendingUpIcon className="h-4 w-4 mr-1" />
              ) : (
                <TrendingDownIcon className="h-4 w-4 mr-1" />
              )}
              {trend.value}
            </div>
          )}
        </div>
        <div className={`p-3 rounded-full bg-gradient-to-r ${
          color === 'teal' ? 'from-teal-100 to-teal-200' :
          color === 'orange' ? 'from-orange-100 to-orange-200' :
          color === 'blue' ? 'from-blue-100 to-blue-200' :
          'from-gray-100 to-gray-200'
        }`}>
          <Icon className={`h-6 w-6 ${
            color === 'teal' ? 'text-teal-600' :
            color === 'orange' ? 'text-orange-600' :
            color === 'blue' ? 'text-blue-600' :
            'text-gray-600'
          }`} />
        </div>
      </div>
    </div>
  );

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Categories"
          value={analytics.totalCategories}
          icon={TagIcon}
          color="teal"
        />
        <StatCard
          title="Active Categories"
          value={analytics.activeCategories}
          icon={ChartBarIcon}
          color="blue"
        />
        <StatCard
          title="Total Customers"
          value={analytics.totalCustomers}
          icon={UsersIcon}
          color="orange"
        />
        <StatCard
          title="Average Discount"
          value={`${analytics.averageDiscount}%`}
          icon={CurrencyDollarIcon}
          color="teal"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Categories */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <UsersIcon className="h-5 w-5 mr-2 text-teal-600" />
            Top Categories by Customers
          </h3>
          <div className="space-y-3">
            {analytics.topCategories.length > 0 ? (
              analytics.topCategories.map((category, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: category.color }}
                    />
                    <span className="text-sm font-medium text-gray-900">
                      {category.name}
                    </span>
                  </div>
                  <span className="text-sm font-semibold text-gray-600">
                    {category.customers} customers
                  </span>
                </div>
              ))
            ) : (
              <p className="text-sm text-gray-500 text-center py-4">
                No customer data available
              </p>
            )}
          </div>
        </div>

        {/* Discount Distribution */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <CurrencyDollarIcon className="h-5 w-5 mr-2 text-orange-600" />
            Discount Distribution
          </h3>
          <div className="space-y-3">
            {analytics.discountDistribution.length > 0 ? (
              analytics.discountDistribution.map((range, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-900">
                    {range.range}
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-teal-500 to-orange-500 h-2 rounded-full"
                        style={{
                          width: `${(range.count / analytics.totalCategories) * 100}%`
                        }}
                      />
                    </div>
                    <span className="text-sm font-semibold text-gray-600 w-8">
                      {range.count}
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-sm text-gray-500 text-center py-4">
                No discount data available
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Status Overview */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <ChartBarIcon className="h-5 w-5 mr-2 text-teal-600" />
          Category Status Overview
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {analytics.activeCategories}
            </div>
            <div className="text-sm text-green-700 mt-1">Active Categories</div>
            <div className="text-xs text-green-600 mt-1">
              {analytics.totalCategories > 0 
                ? `${((analytics.activeCategories / analytics.totalCategories) * 100).toFixed(1)}%`
                : '0%'
              }
            </div>
          </div>
          <div className="text-center p-4 bg-red-50 rounded-lg">
            <div className="text-2xl font-bold text-red-600">
              {analytics.inactiveCategories}
            </div>
            <div className="text-sm text-red-700 mt-1">Inactive Categories</div>
            <div className="text-xs text-red-600 mt-1">
              {analytics.totalCategories > 0 
                ? `${((analytics.inactiveCategories / analytics.totalCategories) * 100).toFixed(1)}%`
                : '0%'
              }
            </div>
          </div>
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {analytics.totalCategories}
            </div>
            <div className="text-sm text-blue-700 mt-1">Total Categories</div>
            <div className="text-xs text-blue-600 mt-1">
              {analytics.averageDiscount}% avg discount
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerCategoriesAnalytics;

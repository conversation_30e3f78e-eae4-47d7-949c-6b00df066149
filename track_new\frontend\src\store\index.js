import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { combineReducers } from '@reduxjs/toolkit';

// Import slices
import authReducer from './slices/authSlice';
import uiReducer from './slices/uiSlice';
import customerReducer from './slices/customerSlice';
import customerCategoryReducer from './slices/customerCategorySlice';
import serviceReducer from './slices/serviceSlice';
import dashboardReducer from './slices/dashboardSlice';
import expenseReducer from './slices/expenseSlice';
import leadReducer from './slices/leadSlice';
import userReducer from './slices/userSlice';
import employeeReducer from './slices/employeeSlice';
import salesReducer from './slices/salesSlice';
import notificationReducer from './slices/notificationSlice';

// Persist configuration
const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth'], // Only persist auth state
};

// Root reducer
const rootReducer = combineReducers({
  auth: authReducer,
  ui: uiReducer,
  customers: customerReducer,
  customerCategories: customerCategoryReducer,
  services: serviceReducer,
  dashboard: dashboardReducer,
  expenses: expenseReducer,
  leads: leadReducer,
  users: userReducer,
  employees: employeeReducer,
  sales: salesReducer,
  notifications: notificationReducer,
});

// Persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        ignoredPaths: ['register'],
      },
    }),
  devTools: import.meta.env.DEV,
});

export const persistor = persistStore(store);
export default store;

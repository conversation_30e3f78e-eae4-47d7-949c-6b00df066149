import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import customerCategoryService from '../../services/customerCategoryService';

// Async thunks for API calls
export const fetchCustomerCategories = createAsyncThunk(
  'customerCategories/fetchCustomerCategories',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await customerCategoryService.getCustomerCategories(params);
      if (response.success) {
        return response.data;
      } else {
        return rejectWithValue(response.message || 'Failed to fetch customer categories');
      }
    } catch (error) {
      return rejectWithValue(error.message || 'Network error occurred');
    }
  }
);

export const createCustomerCategory = createAsyncThunk(
  'customerCategories/createCustomerCategory',
  async (categoryData, { rejectWithValue }) => {
    try {
      const response = await customerCategoryService.createCustomerCategory(categoryData);
      if (response.success) {
        return response.data;
      } else {
        return rejectWithValue(response.message || 'Failed to create customer category');
      }
    } catch (error) {
      return rejectWithValue(error.message || 'Network error occurred');
    }
  }
);

export const updateCustomerCategory = createAsyncThunk(
  'customerCategories/updateCustomerCategory',
  async ({ id, categoryData }, { rejectWithValue }) => {
    try {
      const response = await customerCategoryService.updateCustomerCategory(id, categoryData);
      if (response.success) {
        return response.data;
      } else {
        return rejectWithValue(response.message || 'Failed to update customer category');
      }
    } catch (error) {
      return rejectWithValue(error.message || 'Network error occurred');
    }
  }
);

export const deleteCustomerCategory = createAsyncThunk(
  'customerCategories/deleteCustomerCategory',
  async (id, { rejectWithValue }) => {
    try {
      const response = await customerCategoryService.deleteCustomerCategory(id);
      if (response.success) {
        return id;
      } else {
        return rejectWithValue(response.message || 'Failed to delete customer category');
      }
    } catch (error) {
      return rejectWithValue(error.message || 'Network error occurred');
    }
  }
);

export const fetchCustomerCategoryStats = createAsyncThunk(
  'customerCategories/fetchCustomerCategoryStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await customerCategoryService.getCustomerCategoryStats();
      if (response.success) {
        return response.data;
      } else {
        return rejectWithValue(response.message || 'Failed to fetch customer category statistics');
      }
    } catch (error) {
      return rejectWithValue(error.message || 'Network error occurred');
    }
  }
);

// Initial state
const initialState = {
  categories: [],
  totalCategories: 0,
  currentPage: 1,
  itemsPerPage: 10,
  totalPages: 1,
  loading: false,
  error: null,
  
  // Search and filter state
  searchTerm: '',
  statusFilter: 'all', // 'all', 'active', 'inactive'
  sortBy: 'sortOrder',
  sortOrder: 'asc',
  
  // UI state
  viewMode: 'card', // 'card' or 'table'
  selectedCategory: null,
  
  // Statistics
  stats: {
    totalCategories: 0,
    activeCategories: 0,
    inactiveCategories: 0,
    totalCustomers: 0,
    averageDiscount: 0
  },
  statsLoading: false,
  statsError: null,
  
  // Operation states
  creating: false,
  updating: false,
  deleting: false,
  createError: null,
  updateError: null,
  deleteError: null
};

// Customer Categories slice
const customerCategorySlice = createSlice({
  name: 'customerCategories',
  initialState,
  reducers: {
    // UI actions
    setViewMode: (state, action) => {
      state.viewMode = action.payload;
    },
    setSelectedCategory: (state, action) => {
      state.selectedCategory = action.payload;
    },
    setCurrentPage: (state, action) => {
      state.currentPage = action.payload;
    },
    setItemsPerPage: (state, action) => {
      state.itemsPerPage = action.payload;
      state.currentPage = 1; // Reset to first page
    },
    
    // Search and filter actions
    setSearchTerm: (state, action) => {
      state.searchTerm = action.payload;
      state.currentPage = 1; // Reset to first page
    },
    setStatusFilter: (state, action) => {
      state.statusFilter = action.payload;
      state.currentPage = 1; // Reset to first page
    },
    setSortBy: (state, action) => {
      state.sortBy = action.payload;
      state.currentPage = 1; // Reset to first page
    },
    setSortOrder: (state, action) => {
      state.sortOrder = action.payload;
      state.currentPage = 1; // Reset to first page
    },
    
    // Clear errors
    clearError: (state) => {
      state.error = null;
      state.createError = null;
      state.updateError = null;
      state.deleteError = null;
      state.statsError = null;
    },
    
    // Reset state
    resetState: (state) => {
      return { ...initialState };
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch customer categories
      .addCase(fetchCustomerCategories.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCustomerCategories.fulfilled, (state, action) => {
        state.loading = false;
        state.categories = action.payload.categories || [];
        state.totalCategories = action.payload.pagination?.totalItems || 0;
        state.totalPages = action.payload.pagination?.totalPages || 1;
        state.currentPage = action.payload.pagination?.currentPage || 1;
        state.itemsPerPage = action.payload.pagination?.itemsPerPage || 10;
      })
      .addCase(fetchCustomerCategories.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Create customer category
      .addCase(createCustomerCategory.pending, (state) => {
        state.creating = true;
        state.createError = null;
      })
      .addCase(createCustomerCategory.fulfilled, (state, action) => {
        state.creating = false;
        // Add new category to the list
        state.categories.unshift(action.payload);
        state.totalCategories += 1;
      })
      .addCase(createCustomerCategory.rejected, (state, action) => {
        state.creating = false;
        state.createError = action.payload;
      })
      
      // Update customer category
      .addCase(updateCustomerCategory.pending, (state) => {
        state.updating = true;
        state.updateError = null;
      })
      .addCase(updateCustomerCategory.fulfilled, (state, action) => {
        state.updating = false;
        // Update category in the list
        const index = state.categories.findIndex(cat => cat.id === action.payload.id);
        if (index !== -1) {
          state.categories[index] = action.payload;
        }
        // Update selected category if it's the same
        if (state.selectedCategory && state.selectedCategory.id === action.payload.id) {
          state.selectedCategory = action.payload;
        }
      })
      .addCase(updateCustomerCategory.rejected, (state, action) => {
        state.updating = false;
        state.updateError = action.payload;
      })
      
      // Delete customer category
      .addCase(deleteCustomerCategory.pending, (state) => {
        state.deleting = true;
        state.deleteError = null;
      })
      .addCase(deleteCustomerCategory.fulfilled, (state, action) => {
        state.deleting = false;
        // Remove category from the list
        state.categories = state.categories.filter(cat => cat.id !== action.payload);
        state.totalCategories -= 1;
        // Clear selected category if it's the deleted one
        if (state.selectedCategory && state.selectedCategory.id === action.payload) {
          state.selectedCategory = null;
        }
      })
      .addCase(deleteCustomerCategory.rejected, (state, action) => {
        state.deleting = false;
        state.deleteError = action.payload;
      })
      
      // Fetch customer category stats
      .addCase(fetchCustomerCategoryStats.pending, (state) => {
        state.statsLoading = true;
        state.statsError = null;
      })
      .addCase(fetchCustomerCategoryStats.fulfilled, (state, action) => {
        state.statsLoading = false;
        state.stats = action.payload;
      })
      .addCase(fetchCustomerCategoryStats.rejected, (state, action) => {
        state.statsLoading = false;
        state.statsError = action.payload;
      });
  }
});

// Export actions
export const {
  setViewMode,
  setSelectedCategory,
  setCurrentPage,
  setItemsPerPage,
  setSearchTerm,
  setStatusFilter,
  setSortBy,
  setSortOrder,
  clearError,
  resetState
} = customerCategorySlice.actions;

// Selectors
export const selectCustomerCategories = (state) => state.customerCategories.categories;
export const selectCustomerCategoriesLoading = (state) => state.customerCategories.loading;
export const selectCustomerCategoriesError = (state) => state.customerCategories.error;
export const selectCustomerCategoryStats = (state) => state.customerCategories.stats;
export const selectCustomerCategoryPagination = (state) => ({
  currentPage: state.customerCategories.currentPage,
  totalPages: state.customerCategories.totalPages,
  itemsPerPage: state.customerCategories.itemsPerPage,
  totalItems: state.customerCategories.totalCategories
});
export const selectCustomerCategoryFilters = (state) => ({
  searchTerm: state.customerCategories.searchTerm,
  statusFilter: state.customerCategories.statusFilter,
  sortBy: state.customerCategories.sortBy,
  sortOrder: state.customerCategories.sortOrder
});
export const selectCustomerCategoryUI = (state) => ({
  viewMode: state.customerCategories.viewMode,
  selectedCategory: state.customerCategories.selectedCategory
});

export default customerCategorySlice.reducer;

import React, { useState } from 'react';
import {
  PencilIcon,
  TrashIcon,
  EyeIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  Squares2X2Icon,
  Bars3Icon,
  TagIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import CustomerCategoriesPagination from './CustomerCategoriesPagination';

/**
 * Customer Categories List Component
 * 
 * Displays customer categories in card or table view with TrackNew design consistency
 * Features:
 * - Dual view modes (Card/Table)
 * - Advanced search and filtering
 * - Sorting capabilities
 * - Pagination
 * - Teal/orange color scheme with 30-40% height reduction
 */
const CustomerCategoriesList = ({
  categories = [],
  viewMode = 'card',
  onEdit,
  onView,
  onDelete,
  searchTerm = '',
  onSearch,
  statusFilter = 'all',
  onFilterChange,
  sortBy = 'sortOrder',
  sortOrder = 'asc',
  onSort,
  currentPage = 1,
  totalPages = 1,
  itemsPerPage = 10,
  totalItems = 0,
  onPageChange,
  onItemsPerPageChange
}) => {
  const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm);

  // Handle search input
  const handleSearchSubmit = (e) => {
    e.preventDefault();
    onSearch(localSearchTerm);
  };

  // Status filter options
  const statusOptions = [
    { value: 'all', label: 'All Categories', count: totalItems },
    { value: 'active', label: 'Active', count: categories.filter(c => c.isActive).length },
    { value: 'inactive', label: 'Inactive', count: categories.filter(c => !c.isActive).length }
  ];

  // Sort indicator
  const SortIcon = ({ field }) => {
    if (sortBy !== field) return null;
    return sortOrder === 'asc' ? 
      <ArrowUpIcon className="h-4 w-4 ml-1" /> : 
      <ArrowDownIcon className="h-4 w-4 ml-1" />;
  };

  // Enhanced Pagination component
  const Pagination = () => (
    <CustomerCategoriesPagination
      currentPage={currentPage}
      totalPages={totalPages}
      itemsPerPage={itemsPerPage}
      totalItems={totalItems}
      onPageChange={onPageChange}
      onItemsPerPageChange={onItemsPerPageChange}
    />
  );

  return (
    <div className="space-y-4">
      {/* Search and Filter Bar */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          {/* Search */}
          <form onSubmit={handleSearchSubmit} className="flex-1 max-w-md">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={localSearchTerm}
                onChange={(e) => setLocalSearchTerm(e.target.value)}
                placeholder="Search categories..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-teal-500 focus:border-teal-500"
              />
            </div>
          </form>

          {/* Status Filter */}
          <div className="flex items-center space-x-4">
            <div className="flex rounded-md shadow-sm">
              {statusOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => onFilterChange(option.value)}
                  className={`px-3 py-2 text-sm font-medium border ${
                    statusFilter === option.value
                      ? 'bg-teal-600 text-white border-teal-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  } ${
                    option.value === 'all' ? 'rounded-l-md' : 
                    option.value === 'inactive' ? 'rounded-r-md' : ''
                  }`}
                >
                  {option.label}
                  <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
                    statusFilter === option.value
                      ? 'bg-white text-teal-600'
                      : 'bg-gray-100 text-gray-600'
                  }`}>
                    {option.count}
                  </span>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Categories Display */}
      {categories.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <TagIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No categories found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm ? 'Try adjusting your search terms.' : 'Get started by creating a new customer category.'}
          </p>
        </div>
      ) : viewMode === 'card' ? (
        /* Card View */
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {categories.map((category) => (
            <div
              key={category.id}
              className="bg-white rounded-lg shadow hover:shadow-md transition-shadow duration-200 border border-gray-200 h-40" // 30-40% height reduction
            >
              <div className="p-4 h-full flex flex-col">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: category.color || '#3B82F6' }}
                    />
                    <h3 className="text-sm font-semibold text-gray-900 truncate">
                      {category.categoryName}
                    </h3>
                  </div>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    category.isActive
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {category.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>

                <p className="text-xs text-gray-600 mb-2 flex-1 line-clamp-2">
                  {category.description || 'No description'}
                </p>

                <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                  <span>Discount: {category.discountPercentage || 0}%</span>
                  <span>Customers: {category.customerCount || 0}</span>
                </div>

                <div className="flex items-center justify-end space-x-2">
                  <button
                    onClick={() => onView(category)}
                    className="p-1 text-gray-400 hover:text-teal-600 transition-colors"
                    title="View"
                  >
                    <EyeIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => onEdit(category)}
                    className="p-1 text-gray-400 hover:text-orange-600 transition-colors"
                    title="Edit"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => onDelete(category)}
                    className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                    title="Delete"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        /* Table View */
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gradient-to-r from-teal-50 to-orange-50">
              <tr>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => onSort('categoryName')}
                >
                  <div className="flex items-center">
                    Category Name
                    <SortIcon field="categoryName" />
                  </div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => onSort('discountPercentage')}
                >
                  <div className="flex items-center">
                    Discount
                    <SortIcon field="discountPercentage" />
                  </div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customers
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {categories.map((category) => (
                <tr key={category.id} className="hover:bg-gray-50 h-12"> {/* Reduced row height */}
                  <td className="px-6 py-2 whitespace-nowrap">
                    <div className="flex items-center">
                      <div
                        className="w-3 h-3 rounded-full mr-3"
                        style={{ backgroundColor: category.color || '#3B82F6' }}
                      />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {category.categoryName}
                        </div>
                        <div className="text-xs text-gray-500 truncate max-w-xs">
                          {category.description}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-2 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      category.isActive
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {category.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-900">
                    {category.discountPercentage || 0}%
                  </td>
                  <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-900">
                    {category.customerCount || 0}
                  </td>
                  <td className="px-6 py-2 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={() => onView(category)}
                        className="text-teal-600 hover:text-teal-900 transition-colors"
                        title="View"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => onEdit(category)}
                        className="text-orange-600 hover:text-orange-900 transition-colors"
                        title="Edit"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => onDelete(category)}
                        className="text-red-600 hover:text-red-900 transition-colors"
                        title="Delete"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          
          <Pagination />
        </div>
      )}
    </div>
  );
};

export default CustomerCategoriesList;
